# Gateway Replacement Flow - Technical Documentation

## Table of Contents
1. [Overview](#overview)
2. [System Architecture](#system-architecture)
3. [Execution Flow](#execution-flow)
4. [Key Components](#key-components)
5. [Data Structures](#data-structures)
6. [API Specifications](#api-specifications)
7. [Error Handling](#error-handling)
8. [Dependencies](#dependencies)
9. [Configuration](#configuration)
10. [Testing](#testing)
11. [Future Improvements](#future-improvements)

## Overview

The Gateway Replacement Flow is a comprehensive system designed to handle the replacement of Carrier Japan Corporation gateways in the AHP (Advanced HVAC Platform) service. This system manages the complete lifecycle of gateway replacement, including device lookup updates, device view migrations, and historical event tracking.


### Purpose
- Replace old gateways with new ones while maintaining data integrity
- Update all associated device views and relationships
- Maintain comprehensive audit trails for compliance and troubleshooting
- Ensure seamless transition with minimal service disruption

### Key Features
- **Full and Partial Replacement Support**: Handles both complete gateway replacements and partial migrations (Only Full replacement is in current scope for CJC)
- **Atomic Operations**: Ensures data consistency through transactional operations
- **Comprehensive Logging**: Maintains detailed audit trails for all replacement activities
- **Error Recovery**: Robust error handling with rollback capabilities

## System Architecture
Check this Documentation For more details: https://carrier-digital.atlassian.net/wiki/spaces/SMART/pages/3825500261/CJC+Gateway+Replacement+Flow+Implementation

```
┌─────────────────────┐    ┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Master Data        │───▶│   Lambda Entry  │───▶│ Gateway Replace  │───▶│  External APIs  │
│  Handler Lambda     │    │     Point       │    │    Service       │    │   (DLS/GQL)     │
│  (Trigger Source)   │    │ (CSSevenSync)   │    │                  │    │                 │
└─────────────────────┘    └─────────────────┘    └──────────────────┘    └─────────────────┘
                                                            │
                                                            ▼
                                                   ┌──────────────────┐
                                                   │  Service Layer   │
                                                   │                  │
                                                   │ ┌──────────────┐ │
                                                   │ │ Gateway Info │ │
                                                   │ │   Service    │ │
                                                   │ └──────────────┘ │
                                                   │ ┌──────────────┐ │
                                                   │ │ Device View  │ │
                                                   │ │   Service    │ │
                                                   │ └──────────────┘ │
                                                   │ ┌──────────────┐ │
                                                   │ │Event History │ │
                                                   │ │   Service    │ │
                                                   │ └──────────────┘ │
                                                   └──────────────────┘
                                                            │
                                                            ▼
                                                   ┌──────────────────┐
                                                   │  Data Layer      │
                                                   │                  │
                                                   │ ┌──────────────┐ │
                                                   │ │   DynamoDB   │ │
                                                   │ └──────────────┘ │
                                                   │ ┌──────────────┐ │
                                                   │ │  DocumentDB  │ │
                                                   │ └──────────────┘ │
                                                   └──────────────────┘
```

## Execution Flow

### High-Level Process Flow

```
START (Master Data Handler Lambda)
  │
  ▼
┌─────────────────────┐
│ Trigger Gateway     │
│ Replacement Request │
└─────────────────────┘
  │
  ▼
┌─────────────────────┐
│ CSSevenSync Lambda  │
│ Entry Point         │
└─────────────────────┘
  │
  ▼
┌─────────────────────┐
│ Validate Request    │
│ Parameters          │
└─────────────────────┘
  │
  ▼
┌─────────────────────┐
│ Check Old Gateway   │
│ Exists              │
└─────────────────────┘
  │
  ▼
┌─────────────────────┐
│ Update Device       │
│ Lookup Service      │
└─────────────────────┘
  │
  ▼
┌─────────────────────┐
│ Update Old Gateway  │
│ Details             │
└─────────────────────┘
  │
  ▼
┌─────────────────────┐
│ Process Device      │
│ Views               │
└─────────────────────┘
  │
  ▼
┌─────────────────────┐
│ Update Event        │
│ History             │
└─────────────────────┘
  │
  ▼
END
```

### Detailed Step-by-Step Execution

#### Phase 1: Trigger and Initialization
1. **Master Data Handler Trigger**
   - Master Data Handler Lambda initiates gateway replacement process
   - Constructs replacement request payload
   - Invokes CSSevenSync Lambda with `action: "gatewayReplacement"`

2. **Request Validation**
   - Validates required fields: `oldGwid`, `newGwid`, `oldGwSerialNumber`, `newGwSerialNumber`, `newGatewayModel`, `replacementType`
   - Returns error response if validation fails

3. **Old Gateway Verification**
   - Queries database to verify old gateway exists
   - Retrieves gateway details including node ID and model
   - Terminates process if old gateway not found

#### Phase 2: Device Lookup Service Update
4. **DLS API Call**
   - Calls `replaceAndRetainEdgeNode` GraphQL mutation
   - Parameters: `groupName: 'ahp'`, `oldEdgeNodeId`, `newEdgeNodeId`, `assetSerialNumber: []`
   - Returns new gateway node ID for subsequent operations
   - Handles API failures with appropriate error responses

#### Phase 3: Gateway Information Update
5. **Old Gateway Update**
   - Marks old gateway as replaced (`isEdgeReplaced: true`)
   - Sets replacement references (`replacedEdgeSN`, `replacedEdgeId`)
   - Processes updates in batches of 20 for performance
   - Tracks success/failure counts for monitoring

#### Phase 4: Device Views Processing
6. **Device Views Retrieval**
   - Fetches all device views associated with old gateway
   - Uses pagination (limit: 50) for large datasets
   - Filters for non-deleted records only

7. **Device Views Migration**
   - **Deletion**: Removes old device views to prevent conflicts
   - **Recreation**: Creates new device views with updated gateway information
   - **ID Generation**: Uses UUID v5 with namespace for consistent ID generation
   - **Batch Processing**: Processes in batches of 25 for optimal performance

#### Phase 5: Historical Event Recording
8. **Gateway Replacement History**
   - Records replacement events for both old and new gateways
   - Captures metadata: models, serial numbers, replacement type
   - Stores in DocumentDB for audit purposes

9. **Asset History Updates**
   - Creates individual history records for each affected asset
   - Links assets to their new gateway relationships
   - Maintains complete audit trail for compliance

## Key Components

### 1. GatewayReplacementService
**Responsibility**: Main orchestrator for the gateway replacement process

**Key Methods**:
- `handleGatewayReplacement(payload)`: Main entry point for replacement requests
- `updateDeviceLookup(oldEdge, newEdge, assetSerialNumbers)`: Updates external device lookup service
- `validateRequest(payload)`: Validates incoming request parameters

**Dependencies**: All other service components

### 2. GatewayInfoHandlingService
**Responsibility**: Manages gateway/edge entity operations

**Key Methods**:
- `gatewayExists(gwid, serialNumber)`: Verifies gateway existence
- `getAndUpdateOldGatewayDetails()`: Updates old gateway with replacement information
- `checkGatewayExists()`: Database query for gateway verification

**Data Operations**: CRUD operations on Edge entities

### 3. DeviceViewInfoHandlingService
**Responsibility**: Manages device view entities and their relationships

**Key Methods**:
- `getDeviceViews(edgeNodeId, edgeSerialNumber)`: Retrieves associated device views
- `deleteDeviceViews(deviceViewIds)`: Removes old device views
- `updateDeviceViews()`: Creates new device views with updated gateway information
- `generateDeviceViewId()`: Creates consistent UUIDs for device views

**Features**:
- Pagination support for large datasets
- Batch processing for performance optimization
- UUID v5 generation for consistent identifiers

### 4. LookupGqlService
**Responsibility**: Interfaces with external Device Lookup Service via GraphQL

**Key Methods**:
- `replaceAndRetainEdgeNode(criteria)`: Executes gateway replacement in external system
- `InvokeGraphQL()`: Handles authenticated GraphQL requests

**Features**:
- AWS STS credential management
- Request signing with Signature V4
- Error handling and retry logic

### 5. EventHistoryManagementService
**Responsibility**: Manages audit trails and historical event recording

**Key Methods**:
- `addGatewayReplacementHistory()`: Records gateway replacement events
- `addAssetHistory()`: Records asset-level replacement events
- `prepareGatewayHistoryData()`: Formats gateway event data
- `prepareAssetHistoryData()`: Formats asset event data

**Storage**: DocumentDB for historical event persistence

## Data Structures

### Request Payload
```typescript
interface GatewayReplacementRequest {
  oldGwid: string;           // Old gateway ID
  newGwid: string;           // New gateway ID
  fileName?: string;         // Optional file reference
  oldGwSerialNumber: string; // Old gateway serial number
  newGwSerialNumber: string; // New gateway serial number
  newGatewayModel: string;   // New gateway model
  replacementType: string;   // "full" or "partial"
}
```

### Response Format
```typescript
interface GatewayReplacementResponse {
  success: boolean;
  message?: string;  // Success message
  error?: string;    // Error message
  timestamp: string; // ISO timestamp
}
```

### Edge Entity
```typescript
interface Edge {
  id: string;
  edgeId: string;
  serialNumber: string;
  model: string;
  isEdgeReplaced?: boolean;
  replacedEdgeSN?: string;
  replacedEdgeId?: string;
  // ... additional fields
}
```

### DeviceView Entity
```typescript
interface DeviceView {
  id: string;
  assetNodeId: string;
  assetSerialNumber: string;
  edgeNodeId: string;
  edgeSerialNumber: string;
  // ... location and customer fields
}
```

## API Specifications

### Lambda Entry Points

#### Master Data Handler Lambda
**Responsibility**: Initiates gateway replacement process
**Trigger**: External system or scheduled event
**Action**: Constructs and sends gateway replacement request

#### CSSevenSync Lambda
**Trigger**: Event with `action: "gatewayReplacement"` from Master Data Handler
**Handler**: `CSSevenSyncLambda.onRequest()`
**Responsibility**: Processes gateway replacement requests

### External API Integration
**Service**: Device Lookup Service (DLS)
**Endpoint**: GraphQL endpoint with `replaceAndRetainEdgeNode` mutation
**Authentication**: AWS STS with Signature V4

### GraphQL Mutation
```graphql
mutation replaceAndRetainEdgeNode($options: ReplaceEdgeNodeOptions!) {
  replaceAndRetainEdgeNode(options: $options) {
    bindingPlatforms
    createdAt
    deviceTableId
    edgeId
    edgeNodeId
    externalId
    groupId
    topicPart
  }
}
```

## Error Handling

### Validation Errors
- **Missing Required Fields**: Returns specific field names in error message
- **Invalid Data Types**: Type validation with descriptive error messages

### Service Errors
- **Gateway Not Found**: Specific error for missing old gateway
- **DLS API Failures**: Wrapped external API errors with context
- **Database Errors**: Caught and logged with operation context

### Recovery Mechanisms
- **Logging**: Comprehensive error logging for troubleshooting

### Error Response Format
```json
{
  "success": false,
  "error": "Descriptive error message",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Dependencies

### External Services
- **Device Lookup Service (DLS)**: GraphQL API for device management
- **AWS STS**: For credential management and API authentication

### AWS Services
- **DynamoDB**: Primary data storage for entities
- **DocumentDB**: Historical event storage
- **Lambda**: Serverless execution environment

### Libraries and Frameworks
- **@carrier-io/backend-lib-node-sdk**: Core SDK for entity management
- **uuid**: UUID generation for consistent identifiers
- **@aws-sdk/signature-v4**: AWS request signing
- **node-fetch**: HTTP client for external API calls

### Internal Dependencies
- **Entity Classes**: Edge, DeviceView, Customer, Location, Asset
- **Configuration Services**: STS configuration, environment variables

## Configuration

### Environment Variables
```bash
DEVICE_LOOKUP_API_URL=https://devicelookup.api.dev.carrier.io/graphql
DEVICE_LOOKUP_API_TOKEN=<api-token>
UUID_NAME_SPACE=944640ed-f732-49c0-84e8-a02d4e527b89
ASSET_TOPIC=spBv1.0/zephyr/DBIRTH
EDGE_TOPIC=spBv1.0/zephyr/NBIRTH
```

### Batch Processing Limits
Safer Implementation technique, Currently one Gateway is handled per request
- **Gateway Updates**: 20 records per batch
- **Device View Updates**: 25 records per batch
- **Device View Retrieval**: 50 records per fetch

### UUID Configuration
- **Namespace**: `944640ed-f732-49c0-84e8-a02d4e527b89`
- **Version**: UUID v5 (SHA-1 based)
- **Format**: `{gatewayId}-{assetSerialNumber}`

## Testing

### Test Coverage
The system includes comprehensive test coverage with:

- **Unit Tests**: Individual service method testing
- **Integration Tests**: End-to-end flow testing
- **Mock Data**: Realistic test data for various scenarios

### Test Scenarios
1. **Successful Full Replacement**: Complete gateway replacement with device views
2. **Successful Partial Replacement**: Gateway replacement without device migration (Not in scope curremtly)
3. **Validation Failures**: Missing required fields testing
4. **Service Failures**: External API failure handling
5. **Edge Cases**: Empty device views, deletion failures

### Mock Services
- **Gateway Info Service**: Mocked database operations
- **Device View Service**: Mocked CRUD operations
- **Lookup Service**: Mocked GraphQL API responses
- **Event History Service**: Mocked event recording

## References and Additional Resources

### Confluence Documentation

#### Gateway Replacement Manual Steps
**Link**: [Gateway Replacement Manual Steps](https://carrier-digital.atlassian.net/wiki/spaces/SMART/pages/3734307199/Gateway+Replacement+Manual+Steps)  
**Description**: Step-by-step manual procedures for gateway replacement operations

#### CJC Gateway Replacement Flow Implementation
**Link**: [CJC Gateway Replacement Flow Implementation](https://carrier-digital.atlassian.net/wiki/spaces/SMART/pages/3825500261/CJC+Gateway+Replacement+Flow+Implementation)  
**Description**: Technical implementation details and architecture documentation

#### Gateway Replacement Operation Manual
**Link**: [Gateway Replacement Operation Manual](https://carrier-digital.atlassian.net/wiki/spaces/SMART/pages/3753148630/Gateway+Replacement+Operation+Manual)  
**Description**: Operational procedures and troubleshooting guide for gateway replacements

### Support and Contact Information

**Development Team**: Hyderabad Samurais 
**Technical Support**: For questions regarding this documentation or system implementation  
**Escalation**: Contact team leads for critical issues or architectural decisions

---

## Document Information

**Document Version**: 1.0  
**Last Updated**: July 2025  
**Document Type**: Technical Architecture and Implementation Guide  
**Audience**: Development Team, DevOps, Technical Support
**Author**: ijazahammad[dot]shaik[at]carrier[dot]com 

