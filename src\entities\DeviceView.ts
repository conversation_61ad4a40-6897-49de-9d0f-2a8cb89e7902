import { BaseNodeEntityClass, NodeFieldScope, entityField, entityName } from '@carrier-io/backend-lib-node-sdk';

@entityName('deviceview')
export class DeviceView extends BaseNodeEntityClass {
  @entityField(NodeFieldScope.custom)
  assetNodeId?: string;

  @entityField(NodeFieldScope.custom)
  assetSFRecordId?: string;

  @entityField(NodeFieldScope.custom)
  assetSerialNumber?: string;

  @entityField(NodeFieldScope.custom)
  modelNumber?: string;

  @entityField(NodeFieldScope.custom)
  designationName?: string;

  @entityField(NodeFieldScope.custom)
  designationNameAlias?: string;

  @entityField(NodeFieldScope.custom)
  productType?: string;

  @entityField(NodeFieldScope.custom)
  isOnboarded?: boolean;

  @entityField(NodeFieldScope.custom)
  isCommissioned?: boolean;

  @entityField(NodeFieldScope.custom)
  isConnected?: boolean;

  @entityField(NodeFieldScope.custom)
  isAssetMigrated?: boolean;

  @entityField(NodeFieldScope.custom)
  isAssetOnline?: boolean;

  @entityField(NodeFieldScope.custom)
  cioTags?: string;

  @entityField(NodeFieldScope.custom)
  picControllerVersion?: string;

  @entityField(NodeFieldScope.custom)
  edgeNodeId?: string;

  @entityField(NodeFieldScope.custom)
  edgeSFRecordId?: string;

  @entityField(NodeFieldScope.custom)
  edgeSerialNumber?: string;

  @entityField(NodeFieldScope.custom)
  isEdgeMigrated?: boolean;

  @entityField(NodeFieldScope.custom)
  isEdgeOnline?: boolean;

  @entityField(NodeFieldScope.custom)
  customerNodeId?: string;

  @entityField(NodeFieldScope.custom)
  customerSFRecordId: string;

  @entityField(NodeFieldScope.custom)
  customerName?: string;

  @entityField(NodeFieldScope.custom)
  customerNameAlias?: string;

  @entityField(NodeFieldScope.custom)
  locationNodeId: string;

  @entityField(NodeFieldScope.custom)
  locationSFRecordId: string;

  @entityField(NodeFieldScope.custom)
  locationName?: string;

  @entityField(NodeFieldScope.custom)
  locationNameAlias?: string;

  @entityField(NodeFieldScope.custom)
  country?: string;

  @entityField(NodeFieldScope.custom)
  countryAlias?: string;

  @entityField(NodeFieldScope.custom)
  locationGlobalRegion?: string;

  @entityField(NodeFieldScope.custom)
  locationGlobalRegionAlias?: string;

  @entityField(NodeFieldScope.custom)
  locationCommercialTerritory?: string;

  @entityField(NodeFieldScope.custom)
  locationCommercialTerritoryAlias?: string;

  @entityField(NodeFieldScope.custom)
  locationMarket?: string;

  @entityField(NodeFieldScope.custom)
  locationMarketAlias?: string;

  @entityField(NodeFieldScope.custom)
  chillerCount?: number;

  @entityField(NodeFieldScope.custom)
  address?: string;

  @entityField(NodeFieldScope.custom)
  series?: string;
}
