import { UpdateParams, UpsertParams } from '@carrier-io/backend-lib-node-sdk';
import { Customer } from '../entities/Customer';
import { Location } from '../entities/Location';
import { Asset } from '../entities/Asset';
import { Edge } from '../entities/Edge';
import { SiteView } from '../entities/SiteView';
import { DeviceView } from '../entities/DeviceView';
import { v5 as uuidv5 } from 'uuid';
import { cjcUoMPreference } from './constants';
import { formatCjcLocationAddress } from '../helpers/formatCjcLocationAddress';
import { EdgeInfo } from '../types/EdgeInfo';
import { logger } from '../utils/logger';

const constants = require('../utils/constants');
const DEFAULT_ASSET_TOPIC = 'spBv1.0/zephyr/DBIRTH';
const assetTopic: string = process.env.ASSET_TOPIC ? process.env.ASSET_TOPIC : DEFAULT_ASSET_TOPIC;
const DEFAULT_EDGE_TOPIC = 'spBv1.0/zephyr/NBIRTH';
const edgeTopic: string = process.env.EDGE_TOPIC ? process.env.EDGE_TOPIC : DEFAULT_EDGE_TOPIC;
const uuidNameSpace = process.env.UUID_NAME_SPACE ?? '944640ed-f732-49c0-84e8-a02d4e527b89';

export const customerUpdateParams = (options: UpdateParams<Customer>): UpdateParams<Customer> => {
  const updateParams: UpdateParams<Customer> = [];

  let uuid = uuidv5([options[0].customerId].join('-'), uuidNameSpace);
  for (const option of options) {
    updateParams.push({
      // updatedBy: constants.CSSeven_Source,
      // updatedAt: new Date(),
      ...option,
      id: uuid,
    });
  }

  logger.info('customer upsertparam:', updateParams[0]);
  return updateParams;
};

export const locationUpdateParams = (options: UpdateParams<Location>): UpdateParams<Location> => {
  const updateParams: UpdateParams<Location> = [];
  let uuid = uuidv5([options[0].locationId].join('-'), uuidNameSpace);
  for (const option of options) {
    updateParams.push({
      // updatedBy: constants.CSSeven_Source,
      // updatedAt: new Date(),
      ...option,
      id: uuid,
    });
  }

  logger.info('location upsertparam:', updateParams[0]);
  return updateParams;
};

export const assetUpdateParams = (
  options: UpsertParams<Asset>,
  customerRecord: Customer,
  edgeRecord: Edge,
  locationRecord: Location,
): UpsertParams<Asset> => {
  const updateParams: UpsertParams<Asset> = options.map((option: any) => {
    return {
      ...option,
      id: [assetTopic, edgeRecord.edgeId?.toUpperCase(), option.deviceId?.toUpperCase()].join('/'),
      name: option.deviceId?.toUpperCase(),
      changedBy: constants.CSSeven_Source,
      changedAt: new Date(),
      assetId: option.assetId,
      serialNumber: option.deviceId?.toUpperCase(),
      cjcserialNumber: option.serialNumber?.toUpperCase(),
      customer: { operation: 'add', ids: [customerRecord.id] },
      edges: { operation: 'add', ids: [edgeRecord.id] },
      location: { operation: 'add', ids: [locationRecord.id] },
      designationName: option.designationName ? option.designationName : option.serialNumber?.toUpperCase(),
      isOnboarded: true,
      warrantyType: option.stdwarrantyType ?? null,
      warrantyExpDate: option.stdwarrantyExpDate ?? null,
      extendedWarrantyExpDate: option.extendedWarrantyExpDate ?? null,
      contractId: option.ContractNameNumber ?? null,
      contractType: option.ContractJobType ?? null,
      contractEndDate: option.contractEndDate ?? null,
      ctrlType: option.ctrlType,
      deviceId: option.deviceId,
      systemOfMeasurement: cjcUoMPreference || 'metric',
    };
  });
  logger.info('assest upsertparam:', updateParams[0]);
  return updateParams;
};

export const assetFulfilledAtUpdateParams = (
  options: UpsertParams<Asset>,
  edgeId: string | undefined,
  fulfilledAt: number | undefined,
): UpsertParams<Asset> => {
  const updateParams: UpsertParams<Asset> = options.map((option) => {
    return {
      ...option,
      id: [assetTopic, edgeId?.toUpperCase(), option.deviceId?.toUpperCase()].join('/'),
      fulfilledAt: fulfilledAt,
    };
  });
  return updateParams;
};

export const deviceViewUpsertParams = (
  assets: UpsertParams<Asset>,
  location: Location,
  customer: Customer,
  edge: Edge,
): UpsertParams<DeviceView> => {
  const upsertParams: UpsertParams<DeviceView> = [];

  for (const asset of assets) {
    const seriesName = asset.model?.match(/^[A-Za-z]+/)?.[0].substring(0, 2) ?? null;
    if (
      (asset.ctrlType == 'UC' && mcAddress(asset.address) == '00') ||
      ['GC', 'MC'].includes(asset.ctrlType ? asset.ctrlType : '')
    ) {
      let chillerCount: number = 0;
      if (asset.ctrlType != 'UC') {
        chillerCount = assets.filter(
          (item: any) =>
            getMainAddress(item.address) === getMainAddress(asset.address) && item.ctrlType.toUpperCase() == 'UC',
        ).length;
        logger.debug('Mc chiller count', chillerCount);
      }
      const edgeSerialNumber = edge.edgeId;
      const assetSerialNumber = asset.deviceId;

      logger.info(
        `edgeSerialNumber:: ${edgeSerialNumber?.toUpperCase()} :: assetSerialNumber:: ${assetSerialNumber?.toUpperCase()} `,
      );

      let uuid = uuidv5([edgeSerialNumber?.toUpperCase(), assetSerialNumber?.toUpperCase()].join('-'), uuidNameSpace);
      upsertParams.push({
        id: uuid,
        assetNodeId: asset.id ?? null,
        assetSFRecordId: asset.assetId ?? null,
        assetSerialNumber: asset.deviceId?.toUpperCase() ?? null,
        modelNumber: asset.modelNumber ?? null,
        designationName: asset.designationName ?? null,
        designationNameAlias: asset?.designationName?.toUpperCase() ?? null,
        productType: asset.productType ?? null,
        isOnboarded: asset.isOnboarded ?? null,
        edgeNodeId: edge.id ?? null,
        edgeSFRecordId: edge.edgeId ?? null,
        edgeSerialNumber: edge.serialNumber?.toUpperCase() ?? null,
        customerNodeId: customer.id ?? null,
        customerSFRecordId: customer.customerId ?? null,
        customerName: customer.name ?? null,
        customerNameAlias: customer?.name?.toUpperCase() ?? null,
        locationNodeId: location.id ?? null,
        locationSFRecordId: location.locationId ?? null,
        locationName: location.name ?? null,
        locationNameAlias: location?.name?.toUpperCase() ?? null,
        locationCommercialTerritory: location.commercialTerritory ?? null,
        locationCommercialTerritoryAlias: location.commercialTerritory?.toUpperCase() ?? null,
        locationMarket: location.market ?? null,
        locationMarketAlias: location.market?.toUpperCase() ?? null,
        country: location.country ?? null,
        countryAlias: location?.country?.toUpperCase() ?? null,
        chillerCount: chillerCount,
        address: asset.address ?? null,
        locationGlobalRegion: location?.globalRegion ?? null,
        locationGlobalRegionAlias: location?.globalRegion?.toUpperCase() ?? null,
        series: seriesName ?? null,
      });
    } else {
      /*
        UC Will be upseted to device view to handle offline count once the dbirth is raised. 
        Note: isOnboarded Should be false for UC so it does not come in Device Manager View
       */
      const edgeSerialNumber = edge.edgeId;
      const assetSerialNumber = asset.deviceId;

      logger.info(
        `edgeSerialNumber:: ${edgeSerialNumber?.toUpperCase()} :: assetSerialNumber:: ${assetSerialNumber?.toUpperCase()} `,
      );
      let uuid = uuidv5([edgeSerialNumber?.toUpperCase(), assetSerialNumber?.toUpperCase()].join('-'), uuidNameSpace);
      upsertParams.push({
        id: uuid,
        locationNodeId: location.id ?? null,
        locationSFRecordId: location.locationId ?? null,
        locationName: location.name ?? null,
        locationNameAlias: location?.name?.toUpperCase() ?? null,
        locationCommercialTerritory: location.commercialTerritory ?? null,
        locationCommercialTerritoryAlias: location.commercialTerritory?.toUpperCase() ?? null,
        locationMarket: location.market ?? null,
        locationMarketAlias: location.market?.toUpperCase() ?? null,
        country: location?.country ?? null,
        countryAlias: location?.country?.toUpperCase() ?? null,
        locationGlobalRegion: location?.globalRegion ?? null,
        locationGlobalRegionAlias: location?.globalRegion?.toUpperCase() ?? null,
        customerName: customer?.name ?? null,
        customerNameAlias: customer?.name?.toUpperCase() ?? null,
        series: seriesName ?? null,
      });
    }
  }
  logger.info('device upsert params:', upsertParams);
  return upsertParams;
};
export const siteViewUpsertParams = (
  assets: Asset[],
  location: Location,
  customer: Customer,
): UpsertParams<SiteView> => {
  const upsertParams: UpsertParams<SiteView> = [];
  const locationAddress = formatCjcLocationAddress(location);

  let edges: any[] = [];
  let gatewayCount = 0;
  let chillerCount = 0;
  let chillerOfflineCount = 0;
  let gatewayOfflineCount = 0;
  let isSitePending = false;
  let sitePendingSince = 0;
  let lastFullfilledAt = 0;
  let masterControllerCount = 0;
  let unitControllerCount = 0;
  let assetSeriesName = new Set<string>();
  let edgeInfo: EdgeInfo[] = [];
  assets.forEach((asset) => {
    const edge: Edge = asset?.edges?.[0] as Edge;
    const assetControlType = asset.ctrlType?.toUpperCase();

    if (assetControlType === 'MC') {
      masterControllerCount++;
    }
    if (assetControlType === 'UC') {
      unitControllerCount++;
    }

    if (asset.model) {
      const equipmentFamily = asset.model || '';
      const matchedSeries = asset.model?.match(/^[A-Za-z]+/)?.[0].substring(0, 2) ?? '';
      assetSeriesName?.add(matchedSeries);
      edgeInfo.push({
        edgeId: edge.id,
        isOnline: edge.isOnline || false,
        chillers: [
          {
            assetId: asset.assetId!,
            equipmentFamily: equipmentFamily,
            isOnline: asset.isOnline || false,
          },
        ],
      });
    }

    if (assetControlType === 'MC' || assetControlType === 'GC') {
      return;
    }
    chillerCount++;
    const isConnected = asset.isOnline && edge?.isOnline;
    if (!isConnected) {
      chillerOfflineCount++;
    }
    //const isUnCommissioned = (!asset.isCommissioned);
    // it is not commisioned it will be ignored for pending sites count
    let isPendingAssetFound = !asset.isCommissioned;
    if (edge) {
      logger.info('edge: ', edge);
      const existingEdge = edges?.find((tempEdge: any) => tempEdge.id === edge.id);
      logger.info('existing edge : ', existingEdge);
      if (!existingEdge) {
        let isOfflineEdgeCounted = existingEdge?.isOfflineEdgeCounted;
        let isgateWayCounted = existingEdge?.isgateWayCounted;
        // // set the edge count for non-migrated uncommisioned chillers & Ignoring the edge count, if edge contains all uncommisioned .
        if (!isPendingAssetFound) {
          gatewayCount++;
          isgateWayCounted = true;
        }
        // set the edge offline count for non-migrated uncommisioned chillers & Ignoring the edge offline count, if edge contains all uncommisioned .
        if (!edge.isOnline && !isPendingAssetFound) {
          gatewayOfflineCount++;
          isOfflineEdgeCounted = true;
        }
        edges.push({ ...edge, isOfflineEdgeCounted, isgateWayCounted });
      } else {
        // set the edge offline count for if edge contains atleaset un commisioned.
        if (!edge.isOnline && !existingEdge.isOfflineEdgeCounted) {
          gatewayOfflineCount++;
          const index = edges.findIndex((edge: any) => edge.id === existingEdge.id);
          if (index !== -1) {
            edges[index].isOfflineEdgeCounted = true;
          }
        }
        // set the edge count for if edge contains atleaset un commisioned non migrated chillers.
        if (!existingEdge.isgateWayCounted) {
          gatewayCount++;
          const index = edges.findIndex((edge: any) => edge.id === existingEdge.id);
          if (index !== -1) {
            edges[index].isgateWayCounted = true;
          }
        }
      }
    }

    if (!isSitePending) {
      // If the previous assets state is already pending then the whole assets i.e site view state will be pending, so we need not check for all the assets.
      // So if the previous assets are not pending, then we assess the current asst to check if it is pending commissioning.
      isSitePending = isPendingAssetFound;
    }

    const assetCreateAt = new Date(asset.createdAt).getTime();
    if (isPendingAssetFound && (sitePendingSince === 0 || assetCreateAt < sitePendingSince)) {
      // for pending Assets, We take the oldest pending chiller createAt. (Lesser epoch time means older date.)
      sitePendingSince = assetCreateAt;
    }

    if (lastFullfilledAt < (asset.fulfilledAt ?? 0)) {
      // fullfillAt value of latest fullfilled chiller
      lastFullfilledAt = asset.fulfilledAt ?? 0;
    }
  });

  let uuid = uuidv5(location.id, uuidNameSpace);
  upsertParams.push({
    id: uuid,
    locationNodeId: location.id,
    locationName: location.name ?? null,
    locationNameAlias: location.name?.toUpperCase() ?? null,
    locationAddress: locationAddress ?? null,
    locationAddressAlias: locationAddress?.toUpperCase() ?? null,
    locationGlobalRegion: location?.globalRegion ?? null,
    locationGlobalRegionAlias: location?.globalRegion?.toUpperCase() ?? null,
    locationCommercialTerritory: location.commercialTerritory ?? null,
    locationCommercialTerritoryAlias: location.commercialTerritory?.toUpperCase() ?? null,
    locationMarket: location.market ?? null,
    locationMarketAlias: location.market?.toUpperCase() ?? null,
    customerNodeId: customer.id ?? null,
    customerName: customer.name ?? null,
    customerNameAlias: customer.name?.toUpperCase() ?? null,
    chillerCount: chillerCount,
    chillerOfflineCount: chillerOfflineCount,
    gatewayCount: gatewayCount,
    gatewayOfflineCount: gatewayOfflineCount,
    activeAlertsCount: 0,
    activeAlarmsCount: 0,
    country: location.country,
    countryAlias: location.country?.toUpperCase() ?? null,
    isPending: isSitePending,
    pendingSince: sitePendingSince,
    lastFullfilledAt,
    onboardEventReceivedAt: new Date(location.createdAt ?? 0).getTime(),
    masterControllerCount,
    unitControllerCount,
    locationLatitude: parseFloat(location?.latitude ?? '0'),
    locationLongitude: parseFloat(location?.longitude ?? '0'),
    series: Array.from(assetSeriesName),
    edges: edgeInfo,
  });
  logger.info('site upsert params:', upsertParams);
  return upsertParams;
};

export const edgeUpsertParams = (event: any, instance: string): UpsertParams<Edge> => {
  const upsertParams: UpsertParams<Edge> = [];
  upsertParams.push({
    id: [edgeTopic, event.gwid?.toUpperCase()].join('/'),
    changedBy: constants.CSSeven_Source,
    changedAt: new Date(),
    edgeId: event.gwid,
    serialNumber: event.gwSerialNumber?.toUpperCase(),
    mac: event.gwMac,
    name: event.gwSerialNumber?.toUpperCase(),
    CSSInstance: instance,
  });
  logger.info('upsertparam:', upsertParams[0]);
  return upsertParams;
};

function getMainAddress(address: string | null | undefined): string | undefined {
  if (address != undefined && address != null) return address.split('_').splice(0, 2).join('_');
  else return undefined;
}

function mcAddress(address: string | null | undefined): string | undefined {
  if (address != undefined && address != null) return address.split('_')[1];
  else return undefined;
}
