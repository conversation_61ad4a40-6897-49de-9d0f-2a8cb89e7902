import 'reflect-metadata';
import { LookupGqlService } from '../services/lookup-gql-service';
import fetch from 'node-fetch';
import { STSConfig } from '../../infra/config/sts-config';
import { SignatureV4 } from '@aws-sdk/signature-v4';

jest.mock('node-fetch');
jest.mock('../../infra/config/sts-config');
jest.mock('@aws-sdk/signature-v4');

describe('LookupGqlService', () => {
    let lookupService: LookupGqlService;
    const mockGetSTSCredentials = jest.fn();
    const mockSign = jest.fn();

    beforeEach(() => {
        process.env.DEVICE_LOOKUP_API_URL = 'https://test.api.com/graphql';
        process.env.DEVICE_LOOKUP_API_TOKEN = 'test-token';

        (STSConfig as jest.Mock).mockImplementation(() => ({
            getSTSCredentials: mockGetSTSCredentials,
        }));

        (SignatureV4 as jest.Mock).mockImplementation(() => ({
            sign: mockSign,
        }));

        lookupService = new LookupGqlService();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should replace and retain edge node successfully', async () => {
        const mockCredentials = {
            region: 'us-east-1',
            accessKeyId: 'mockAccessKeyId',
            secretAccessKey: 'mockSecretAccessKey',
            sessionToken: 'mockSessionToken',
        };

        mockGetSTSCredentials.mockResolvedValueOnce(mockCredentials);
        
        const mockResponse = {
            data: {
                replaceAndRetainEdgeNode: { 
                    edgeId: 'new-edge-id',
                    success: true
                }
            }
        };

        mockSign.mockResolvedValueOnce({
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({}),
            url: 'https://test.api.com/graphql'
        });

        (fetch as unknown as jest.Mock).mockResolvedValueOnce({
            json: jest.fn().mockResolvedValueOnce(mockResponse),
        });

        const criteria = { 
            groupName: 'ahp',
            oldEdgeNodeId: 'old-edge',
            newEdgeNodeId: 'new-edge',
            assetSerialNumber: []
        };
        
        const result = await lookupService.replaceAndRetainEdgeNode(criteria);

        expect(mockGetSTSCredentials).toHaveBeenCalled();
        expect(mockSign).toHaveBeenCalled();
        expect(fetch).toHaveBeenCalled();
        expect(result).toEqual(mockResponse.data.replaceAndRetainEdgeNode);
    });

    it('should throw error when STS credentials fail', async () => {
        mockGetSTSCredentials.mockRejectedValueOnce(new Error('STS failed'));

        const criteria = { test: 'data' };
        
        // STS error happens before try-catch, so original error is thrown
        await expect(lookupService.replaceAndRetainEdgeNode(criteria))
            .rejects.toThrow('STS failed');
    });

    it('should throw error when fetch fails', async () => {
        const mockCredentials = {
            region: 'us-east-1',
            accessKeyId: 'mockAccessKeyId',
            secretAccessKey: 'mockSecretAccessKey',
            sessionToken: 'mockSessionToken',
        };

        mockGetSTSCredentials.mockResolvedValueOnce(mockCredentials);
        mockSign.mockResolvedValueOnce({
            method: 'POST',
            headers: {},
            body: '{}',
            url: 'https://test.api.com/graphql'
        });

        (fetch as unknown as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

        const criteria = { test: 'data' };
        
        await expect(lookupService.replaceAndRetainEdgeNode(criteria))
            .rejects.toBe('failed ReplaceAndRetainEdgeNode API');
    });

    it('should throw error when GraphQL response has no data', async () => {
        const mockCredentials = {
            region: 'us-east-1',
            accessKeyId: 'mockAccessKeyId',
            secretAccessKey: 'mockSecretAccessKey',
            sessionToken: 'mockSessionToken',
        };

        mockGetSTSCredentials.mockResolvedValueOnce(mockCredentials);
        mockSign.mockResolvedValueOnce({
            method: 'POST',
            headers: {},
            body: '{}',
            url: 'https://test.api.com/graphql'
        });

        (fetch as unknown as jest.Mock).mockResolvedValueOnce({
            json: jest.fn().mockResolvedValueOnce({}), // No data field
        });

        const criteria = { test: 'data' };
        
        await expect(lookupService.replaceAndRetainEdgeNode(criteria))
            .rejects.toBe('failed ReplaceAndRetainEdgeNode API');
    });
});