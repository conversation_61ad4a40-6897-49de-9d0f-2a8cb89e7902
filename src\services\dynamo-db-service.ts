import { Asset } from 'entities/Asset';
import { DynamoDB } from '../repositories/dynamo-db';

export class DynamoDBService {
    private dynamoDB: DynamoDB;

    constructor() {
        this.dynamoDB = new DynamoDB();
    }

    // public async upsertDesignData(asset: Asset, edgeSN: string | undefined): Promise<boolean> {
    //     const params = {
    //         TableName: process.env.ASSET_DESIGN_DATA_ZEPHYR_TABLE ?? "",
    //         Key: {
    //             assetSerialNumber: asset.serialNumber,
    //             edgeSerialNumber: edgeSN
    //         },
    //         UpdateExpression:
    //             'set #designData = :designData',
    //         ExpressionAttributeNames: {
    //             '#designData': 'designData'
    //         },
    //         ExpressionAttributeValues: {
    //             ':designData': JSON.stringify(asset.designData)
    //         },
    //         ReturnValues: "NONE"
    //     };

    //     try {
    //         await this.dynamoDB.updateItem(params);
    //         console.log(`Dynamo DB upsertDesignData is suceessful for asseSerialNumber :: ${asset.serialNumber} and edgeSerialNumber :: ${edgeSN}`);
    //         return true;
    //     } catch (error: any) {
    //         console.log("Error in Dynamo DB upsertDesignData", error);
    //         throw error;
    //     }
    // }
}