import { ZephyrApp } from '@zephyr/backend-lib-infrastructure';


export class ConfigVariables {
    
    public static readonly OtelDefaultConfig = {
        logLevel: "INFO",
        otelLogServiceAccountRegion: "us-east-1",
        otelLogServiceAccountLambdaVersion: 9,
        otelLogServiceAccount: "************",
        lambdaExecWrapper: "/opt/otel-initializer",
        otelLogsSamplingRate: "80",
        otelBackendExporters: ["newrelic"],
        otelLogLevel: ["info", "warn", "error"],
        otelServiceName: "ahp-service-csseven-integration",
        disableLogSampling: "false",
    }
    
    /**********************************
     * Environment-Specific Variables *
     **********************************
     * Any variables defined in 'dev' must also have a value defined in the
     * higher environment structures as well.
     **********************************/
    private static readonly dev = {
        ...this.OtelDefaultConfig
    };

    private static readonly qa : typeof this.dev = {
        ...this.OtelDefaultConfig,
        otelLogsSamplingRate: "70",
    };

    private static readonly preprod : typeof this.dev = {
        ...this.OtelDefaultConfig
    };

    private static readonly prod : typeof this.dev = {
        ...this.OtelDefaultConfig,
        otelLogsSamplingRate: "0",
        disableLogSampling: "true",
    };

    private static readonly config: Record<string,typeof this.dev> = {
        dev: this.dev,
        qa: this.qa,
        preprod: this.preprod,
        prod: this.prod
    };

    static _getHostedZoneNameWithPrefix(stageName: string,
                                        zoneName: string,
                                        zonePrefix?: string): string {
        // Determine what the Hosted Zone prefix will be if it is required
        zonePrefix = zonePrefix ?? stageName;

        if (stageName.toLowerCase() != 'prod') {
            // If this is not the production stage/environment, a prefix should
            // added.
            zoneName = `${zonePrefix}.${zoneName}`;
        };
        return zoneName.toLowerCase();
    };

    static from(app : ZephyrApp) {
        // Get the per-environment staging configuration
        const stageConfig = this.config[app.deploymentStage.toLowerCase()];

        // TODO Set this to DNS hostname of where the production environment
        // will be set to. Non-production environments will get the
        // environment name prefixed
        // Example:
        // const zoneName = 'carrier.io';
        const zoneName = 'ahp';

        // TODO Specify a subdomain for where your application will be
        // referenced underneath the aforementioned `zoneName` variable.
        // Example:
        // const subdomainName = 'my-new-application';
        const subdomainName = 'my-new-application';

        return {
            ...stageConfig, ...{
                /**********************************
                 * Environment-Agnostic Variables *
                 **********************************
                 * These are merged with the Environment-Specific variables.
                 **********************************/
                dnsName: this._getHostedZoneNameWithPrefix(
                    `${subdomainName}.${app.deploymentStage.toLowerCase()}`,
                    zoneName),
                zoneName: this._getHostedZoneNameWithPrefix(
                    app.deploymentStage.toLowerCase(),
                    zoneName),
            },
        };
    };
    
};

// vim:expandtab:ft=typescript:sw=4:ts=4
