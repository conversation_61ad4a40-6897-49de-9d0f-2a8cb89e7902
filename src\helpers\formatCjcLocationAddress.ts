import { Location } from '../entities/Location';

const JAPANESE_POSTAL_MARK = '〒';

const PREFECTURE_EXCEPTIONS = {
    TOKYO: '東京都',
    HOKKAIDO: '北海道',
    OSAKA: '大阪府',
    KYOTO: '京都府',
};

const { TOKYO, HOKKAIDO, OSAKA, KYOTO } = PREFECTURE_EXCEPTIONS;

const prefectureLookup = {
    [TOKYO]: TOKYO,
    [HOKKAIDO]: HOKKAIDO,
    [OSAKA]: OSAKA,
    [KYOTO]: KYOTO,
};

export const formatCjcLocationAddress = (
    locationData?: Pick<Location, "street" | "city" | "state" | "zip"> | null
) => {
    if (!locationData) return '';

    const { street, city, state, zip } = locationData;

    const postalCode = zip ? `${JAPANESE_POSTAL_MARK}${zip}` : '';
    const prefecture = prefectureLookup[state ?? ''] ?? state ?? '';

    const locationString = [prefecture, city].filter(Boolean).join(' ');

    const addressParts = [postalCode, locationString, street].filter(Boolean);

    return addressParts.join(' ');
};

