import { connect, connection, ConnectOptions, Mongoose, set } from 'mongoose';

import { DatabaseError } from '../errors';
import { SecretManager } from '../services/secret-manager.service';
import { STAGE } from '../enums';
import { logger } from '@carrier-io/pe-lib-otel-logs';

export interface IDbAdapter {
  connect(): Promise<Mongoose | undefined>;
  closeConnection(): Promise<void>;
}
let dbInstance : string;

const secretManager = new SecretManager();

export class MongoDBAdapter implements IDbAdapter {

  constructor(instance){
    dbInstance = instance;
  }
  public async connect(): Promise<Mongoose | undefined> {
    let dbConnection;

    try {
      let credentials;
      if (process.env.STAGE === STAGE.LOCAL) {
        credentials = {
          username: process.env.DB_USER,
          password: process.env.DB_PASSWORD,
        };
      } else {
        const secretResponse = await secretManager.getSecretValue(process.env.SECRET_DB_KEY || '');
        logger.info("Got data from secretmanager")
        credentials = JSON.parse(secretResponse.SecretString || '');
      }
      console.log("data from secret manager", JSON.stringify(credentials))
      const dbUrl = await MongoDBAdapter.getConnectionURL(credentials?.username, credentials?.password);
      set('strictQuery', false);
      set('debug', (collectionName, method, query, doc) => {
        // eslint-disable-next-line
        logger.debug(`${collectionName}.${method}`, JSON.stringify(query), doc);
      });
      console.log("DB URL", dbUrl)
      dbConnection = await connect(dbUrl, {
        authMechanism: 'SCRAM-SHA-1',
        authSource: 'admin'
      } as ConnectOptions);
      logger.info("connected to DB");
      return dbConnection;
    } catch (error) {
      logger.error("failed to connect: ", error)
      throw new DatabaseError(
        error instanceof Error ? `DB connection error: ${error.message}` : 'Untraceable connection error',
      );
    }

  }

  private static async getConnectionURL(username: string, password: string): Promise<string> {
    let db_url = dbInstance == 'event' ? process.env.EVENT_DB_URL : process.env.ALARM_DB_URL;
    return (db_url ?? '').replace('<username>', username).replace('<password>', password);
  }

  async closeConnection(): Promise<void> {
    await connection?.close();
  }
}
