////////////////////////////
// IMPORTS
////////////////////////////

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@zephyr/backend-lib-infrastructure";

import { Config } from "./config/Config";
import { CjcCsSevenIntegrationStack } from "./stacks/CjcCsSevenIntegrationStack";
import { VpcStack } from "./stacks/Vpc";


////////////////////////////
// APP
////////////////////////////
//

// TODO RENAME THIS
const app = new ZephyrApp({
  appName: "AHPServiceSCSevenIntegration",
});

// initialize configuration
const config = Config.from(app);
////////////////////////////
// STACKS
////////////////////////////
// TODO RENAME THIS
const stackPrefix = "CSevenIntegrationApp";

// TODO RENAME THIS
const getVpcId = (stage: string) => {
  const devVpcId = "vpc-06e31b1b2ea42582c"
  const vpcIds = new Map<string, string>([
    ["dev", "vpc-06e31b1b2ea42582c"],
    ["qa", "vpc-08624174c06839f56"],
    ["preprod", "vpc-0d60e061f92ed71f8"],
    ["prod", "vpc-046efe02252fda85e"]
  ]);
  return vpcIds.get(stage) ?? devVpcId;
}
const vpcId = getVpcId(app.stage.stageName);
const vpcStack = new VpcStack(app.stage, {vpcId}, 'CSSevenIntegrationServiceVPC');
new CjcCsSevenIntegrationStack(app.stage, config, "CSSevenIntegrationService", {
  vpc: vpcStack.vpc
});

// vim:expandtab:ft=typescript:sw=4:ts=4
