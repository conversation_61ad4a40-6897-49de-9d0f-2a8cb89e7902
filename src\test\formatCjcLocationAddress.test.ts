import { formatCjcLocationAddress } from '../helpers/formatCjcLocationAddress';

describe("formatCjcLocationAddress function", () => {
    test("should format Japanese address (Tokyo)", () => {
        const location = {
            street: "1-1 Chiyoda, Kanda",
            city: "千代田区",
            state: "東京都",
            zip: "100-8111",
        };
        const result = formatCjcLocationAddress(location);
        expect(result).toBe("〒100-8111 東京都 千代田区 1-1 Chiyoda, Kanda");
    });

    test("should format Japanese address (Shizuoka)", () => {
        const location = {
            street: "336 Tadehara, Fuji-shi",
            city: "富士市",
            state: "静岡県",
            zip: "4168521",
        };
        const result = formatCjcLocationAddress(location);
        expect(result).toBe("〒4168521 静岡県 富士市 336 Tadehara, Fuji-shi");
    });

    test("should handle missing fields in non-Japanese address", () => {
        const location = {
            street: undefined,
            city: undefined,
            state: "California",
            zip: undefined,
        };
        const result = formatCjcLocationAddress(location);
        expect(result).toBe("California");
    });

    test("should handle missing street in Japanese address", () => {
        const location = {
            street: undefined,
            city: "渋谷区",
            state: "東京都",
            zip: "150-0002",
        };
        const result = formatCjcLocationAddress(location);
        expect(result).toBe("〒150-0002 東京都 渋谷区");
    });

    test("should handle missing city in Japanese address", () => {
        const location = {
            street: "336 Tadehara, Fuji-shi",
            city: undefined,
            state: "静岡県",
            zip: "4168521",
        };
        const result = formatCjcLocationAddress(location);
        expect(result).toBe("〒4168521 静岡県 336 Tadehara, Fuji-shi");
    });

    test("should handle missing zip code in Japanese address", () => {
        const location = {
            street: "336 Tadehara, Fuji-shi",
            city: "富士市",
            state: "静岡県",
            zip: undefined,
        };
        const result = formatCjcLocationAddress(location);
        expect(result).toBe("静岡県 富士市 336 Tadehara, Fuji-shi");
    });

    test("should return address with only available parts", () => {
        const location = {
            street: "",
            city: "Tokyo",
            state: "東京都",
            zip: "",
        };
        const result = formatCjcLocationAddress(location);
        expect(result).toBe("東京都 Tokyo");
    });
});
