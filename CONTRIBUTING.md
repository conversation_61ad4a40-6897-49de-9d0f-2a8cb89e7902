# ahp-service-csseven-integration

## Contents

[TOC]

## Getting Started

Before we start, please, make sure you are familiar with the guidance in the [Contributing Guide Template](https://carrier-digital.atlassian.net/wiki/spaces/SMART/pages/2745040966/)

Provide an overview of this documentation. A sample is provided for reference here:

```
[This guide contains the necessary instructions for developers who will contribute to AHS. It also contains links to the architecture, an overview of the project structure, and details about any prerequisites required. For a more detailed overview and links to related documentation, see the [ReadMe file](https://carrier-digital.atlassian.net/wiki/spaces/SMART/pages/2745040951)].
```

### Prerequisites

* List all prerequisites in bullet form

#### Common
 * AWS-CLI
 * Node 16

#### Mac:
 * Docker-Desktop v2


## Architecture

[ Solution Architecture on Confluence ](link)


## Project Structure

- Describe project-tree structure, purpose of main files, how and where new files should be placed.
- Use bullets if listing out separate items.


## Instructions

```

This section should contain instructions and context.

```

### How to Install

1. Step 1 to install
   * [MAC] Install mac-specific app
   * [WIN] Install windows-specific app
   * [LINUX] Install linux-specific app
2. Step 2 to install
3. Etc


### How to Configure

1. Step 1 to Configure
2. Step 2 to Configure
3. Etc


### How to Develop

1. Step 1 to Develop
2. Step 2 to Develop
3. Etc


### How to Format

1. Step 1 to Formatting
2. Step 2 to Formatting
3. Etc


### How to Deploy from local to Personal Stage

1. Step 1 to Deploy
2. Step 2 to Deploy
3. Etc

### How to Deploy to Dev/Prod

1. Step 1 to Deploy
2. Step 2 to Deploy
3. Etc


```
OR add the appropriate heading and link to a Confluence topic.

```
