import mongoose, { Schema, models } from 'mongoose';
import { EventInDb } from '../models/OobEvent';

const properties = new Schema({
  key: String,
  value: String,
});

const EventSchema = new Schema({
  id: { type: String },
  timestamp: { type: Number },
  type: { type: String, allowNull: false, index: true },
  edgeId: { type: String, allowNull: true },
  edgeSN: { type: String, allowNull: false, index: true },
  serialPort: { type: String, allowNull: true },
  busAddress: { type: String, allowNull: true },
  properties: [properties],
  assetSN: { type: String, allowNull: true },
  assetName: { type: String, allowNull: true },
  assetId: { type: String, allowNull: true },
  name: { type: String },
});

export const EventModel = models.Event || mongoose.model<EventInDb>('Event', EventSchema, 'Event');
