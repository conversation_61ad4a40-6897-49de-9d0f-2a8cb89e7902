# ahp-service-csseven-integration

[TOC]

`Delete All Template Guidance or blocks of template guidance, including this sentence, from your document.`
**Use absolute links or they will break in Code Artifact.**


## Overview

```
Template Guidance:
- Work with <PERSON><PERSON> on the overview. Do not wait until the end of the sprint.
```

### Repository Documentation

| Markdown Link | Description |
| ----------- | ----------- |
| Contributing Guide| Contains the documentation for developers making changes in the repository.|
| Docs ReadMe | Contains Generated Markdown for Classes, Enums, etc|

```
Template Guidance:

In the above table:

- Link to the Contributing guide.
- Link to any other relevant information.
```

### Relevant Confluence Documentation

| Confluence Page | Description |
| ----------- | ----------- |
| Page Title | Description |
| [AHP Glossary](https://carrier-digital.atlassian.net/wiki/spaces/SMART/pages/2455339246/)| Confluence Glossary for AHP Terms and Definitions|

```
Template Guidance for Confluence Documentation Links:

- List and Link to architecture page in Confluence.
- Do not link to epic-specific architecture. It is a static page that will be merged into the main architecture for the service, domain, or other components.
- Include Glossary **AT THE BOTTOM**.
- Remove the page name from the Confluence link. Confluence locates pages by the key (sequence of numbers in the URL right before the page name).
```

## Architecture


```
Link to architecture. Architecture guidance: don't link to the static epic architecture. This link should go to the living architecture for the solution, component, etc.
```


## Examples

```
Template Guidance for Examples:

Optional section. Examples can be code examples or relevant information. Examples should be meaningful.
```

```
    Insert code in code block. Use syntax highlighting when possible.
```
