import { BaseNodeEntityClass, NodeFieldScope, entityField, entityName } from '@carrier-io/backend-lib-node-sdk';
import { EdgeInfo } from '../types/EdgeInfo';

@entityName('siteview')
export class SiteView extends BaseNodeEntityClass {
  @entityField(NodeFieldScope.custom)
  locationNodeId?: string;

  @entityField(NodeFieldScope.custom)
  locationName?: string;

  @entityField(NodeFieldScope.custom)
  locationAddress?: string;

  @entityField(NodeFieldScope.custom)
  locationNameAlias?: string;

  @entityField(NodeFieldScope.custom)
  locationAddressAlias?: string;

  @entityField(NodeFieldScope.custom)
  customerNodeId?: string;

  @entityField(NodeFieldScope.custom)
  customerName?: string;

  @entityField(NodeFieldScope.custom)
  customerNameAlias?: string;

  @entityField(NodeFieldScope.custom)
  chillerCount?: number;

  @entityField(NodeFieldScope.custom)
  chillerOfflineCount?: number;

  @entityField(NodeFieldScope.custom)
  gatewayCount?: number;

  @entityField(NodeFieldScope.custom)
  gatewayOfflineCount?: number;

  @entityField(NodeFieldScope.custom)
  activeAlertsCount?: number;

  @entityField(NodeFieldScope.custom)
  activeAlarmsCount?: number;

  @entityField(NodeFieldScope.custom)
  country?: string;

  @entityField(NodeFieldScope.custom)
  countryAlias?: string;

  @entityField(NodeFieldScope.custom)
  isPending?: boolean;

  @entityField(NodeFieldScope.custom)
  lastFullfilledAt?: number;

  @entityField(NodeFieldScope.custom)
  pendingSince?: number;

  @entityField(NodeFieldScope.custom)
  locationGlobalRegion?: string;

  @entityField(NodeFieldScope.custom)
  locationGlobalRegionAlias?: string;

  @entityField(NodeFieldScope.custom)
  locationCommercialTerritory?: string;

  @entityField(NodeFieldScope.custom)
  locationCommercialTerritoryAlias?: string;

  @entityField(NodeFieldScope.custom)
  locationMarket?: string;

  @entityField(NodeFieldScope.custom)
  locationMarketAlias?: string;

  @entityField(NodeFieldScope.custom)
  onboardEventReceivedAt?: number;

  @entityField(NodeFieldScope.custom)
  masterControllerCount?: number;

  @entityField(NodeFieldScope.custom)
  unitControllerCount?: number;

  @entityField(NodeFieldScope.custom)
  locationLatitude?: number;

  @entityField(NodeFieldScope.custom)
  locationLongitude?: number;

  @entityField(NodeFieldScope.custom)
  series?: string[];

  @entityField(NodeFieldScope.custom)
  edges?: EdgeInfo[];
}
