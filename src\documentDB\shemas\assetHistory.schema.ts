import mongoose, { Schema, models } from 'mongoose';
import { AssetHistoryInDb } from '../models';

const properties = new Schema({
  key: String,
  value: String,
});

const Actor = new Schema({
  type: { type: String, allowNull: false},
  cid: { type: String, allowNull: false},
})

const TargetEntity = new Schema({
  type: { type: String, allowNull: false},
  chillerId: { type: String, allowNull: true},
  cid: { type: String, allowNull: false}
})

const AssetHistorySchema = new Schema({
  createdAt: { type: Number, allowNull: false },
  updatedAt: { type: Number, allowNull: false },
  startTimestamp: { type: Number, allowNull: false },
  actor: { type: Actor, allowNull : false},
  targetEntity: { type: TargetEntity, allowNull: false},
  reason: { type: String, allowNull: true },
  action: { type: String, allowNull: false },
  properties: [properties],
});

export const AssetHistoryModel = models.AssetHistory || mongoose.model<AssetHistoryInDb>('mutinglogs', AssetHistorySchema);
