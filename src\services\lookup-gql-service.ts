import { Request } from 'node-fetch';
import fetch from 'node-fetch';
import { SignatureV4 } from '@aws-sdk/signature-v4';
import { HttpRequest } from '@aws-sdk/protocol-http';
import { Sha256 as sha256 } from '@aws-crypto/sha256-js';

import { STSConfig } from '../../infra/config/sts-config'
import { getEdgeReplacementQuery } from '../gql/query/replaceAndRetainEdge.query';
import { logger } from '../utils/logger';


const lookupURL: string = process.env.DEVICE_LOOKUP_API_URL ?? 'https://devicelookup.api.dev.carrier.io/graphql';
const platformToken: string = process.env.DEVICE_LOOKUP_API_TOKEN ?? '';

export class LookupGqlService {

    private stsObj: STSConfig;

    constructor(){
        this.stsObj = new STSConfig();
    }

    public async replaceAndRetainEdgeNode(criteria: any) {
        const apiUrl = new URL(lookupURL);
        const assumeCredentials = await this.stsObj.getSTSCredentials();
        try {
    
            const replaceAndRetainEdgeNodeQuery = JSON.stringify(getEdgeReplacementQuery(criteria));
            logger.info("edge replacement query: ", replaceAndRetainEdgeNodeQuery)
            const signer = new SignatureV4({
                service: 'execute-api',
                region: assumeCredentials?.region as string || 'us-east-1',
                credentials: {
                    accessKeyId : assumeCredentials?.accessKeyId as string,
                    secretAccessKey: assumeCredentials?.secretAccessKey as string,
                    sessionToken: assumeCredentials?.sessionToken as string,
                },
                sha256,
            });
    
            const requestToBeSigned = new HttpRequest({
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    host: apiUrl.host,
                    'x-carrier-io-api-key': platformToken,
                },
                hostname: apiUrl.host,
                body: replaceAndRetainEdgeNodeQuery,
                path: apiUrl.pathname
            });
    
            const response = await this.InvokeGraphQL(
                signer,
                lookupURL,
                requestToBeSigned
            );
            return response?.data?.replaceAndRetainEdgeNode;
        } catch (error) {
            logger.error("failed ReplaceAndRetainEdgeNode API:", error)
            throw "failed ReplaceAndRetainEdgeNode API"
        }
    }
    private async InvokeGraphQL (defaultSigner: SignatureV4, url: string, requestToBeSigned: HttpRequest) {
        try{
            const signed = await defaultSigner.sign(requestToBeSigned);
            const request = new Request(url, signed);
            const response: any = await fetch(request);
            const data: any = await response.json();
            logger.debug("graphql response", JSON.stringify(data));
            if(!data?.data){
                logger.warn("failed DLS no data returned");
                throw new Error("Graphql call failed.")
               
            }
            return data;
        }catch(err){
            logger.error("failed to api call: ", err)
            throw "failed"
        }
    }
} 
