const mockEdgeDetails = [
    {
        "hasPoint": [],
        "assets": [],
        "createdBy": "carrier-io-pipeline-consumer-LifecycleConsumer-lambda-prod",
        "updatedBy": "zephyr-event-consumer-platformdata-lambda-prod-IoVegh5hIAMEbwg=",
        "id": "80b6252c-fa2a-437c-9d1f-6af1f0a06b74",
        "domain": "edge",
        "createdAt": "2025-03-17T08:35:24.933Z",
        "updatedAt": "2025-04-07T02:18:30.109Z",
        "isDeleted": false,
        "name": "866226060854159",
        "birthNotification": 1743991736000,
        "deathNotification": 1743992308473,
        "model": "CJC_Eye",
        "imei": "866226060854159",
        "iccId": "8981100005707435625",
        "isOnline": false,
        "edgeId": "866226060854159",
        "serialNumber": "KE1W000310",
        "lastEventTimestamp": 1743992308473
    }
]