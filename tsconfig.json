{"extends": "@tsconfig/node20/tsconfig.json", "compilerOptions": {"declaration": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "noImplicitAny": false, "strictNullChecks": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": false, "inlineSourceMap": true, "inlineSources": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "strictPropertyInitialization": false, "types": ["jest", "node"], "baseUrl": "./src", "outDir": "./dist", "paths": {}}, "include": ["src/**/*.ts", "src/**/*.js", "infra/**/*.ts"], "exclude": ["cdk.out"]}