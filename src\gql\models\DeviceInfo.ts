import * as GQL from 'type-graphql';


@GQL.ObjectType()
export class ReplaceAndRetainEdgeNodeResponse {
  @GQL.Field({ nullable: true })
  bindingPlatforms?: [string];

  @GQL.Field({ nullable: true })
  createdAt?: number;

  @GQL.Field({ nullable: true })
  deviceTableId?: string;

  @GQL.Field({ nullable: true })
  edgeId?: string;

  @GQL.Field({ nullable: true })
  edgeNodeId?: number;

  @GQL.Field({ nullable: true })
  externalId?: string;

  @GQL.Field({ nullable: true })
  groupId?: string;

  @GQL.Field({ nullable: true })
  topicPart?: string;
}

@GQL.InputType()
export class ReplaceAndRetainEdgeNodeCriteria {
  @GQL.Field({ nullable: false })
  groupName: string;

  @GQL.Field({ nullable: false })
  oldEdgeNodeId: string;

  @GQL.Field({ nullable: false })
  newEdgeNodeId: string;
}