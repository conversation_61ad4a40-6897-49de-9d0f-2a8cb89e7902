/* eslint import/no-cycle: 0 */
import {
  BaseNodeEntityClass,
  NodeFieldScope,
  RelationToMany,
  entityField,
  entityName,
  entityRelation,
} from '@carrier-io/backend-lib-node-sdk';

import { Asset } from './Asset';

@entityName('location')
export class Location extends BaseNodeEntityClass {
  @entityField(NodeFieldScope.custom)
  locationId: string;

  @entityField(NodeFieldScope.custom)
  name?: string;

  @entityField(NodeFieldScope.custom)
  street?: string;

  @entityField(NodeFieldScope.custom)
  city?: string;

  @entityField(NodeFieldScope.custom)
  state?: string;

  @entityField(NodeFieldScope.custom)
  country?: string;

  @entityField(NodeFieldScope.custom)
  zip?: string;

  @entityField(NodeFieldScope.custom)
  latitude?: string;

  @entityField(NodeFieldScope.custom)
  longitude?: string;

  @entityField(NodeFieldScope.custom)
  timezone?: string;

  @entityField(NodeFieldScope.custom)
  commercialTerritory?: string;

  @entityField(NodeFieldScope.custom)
  market?: string;

  @entityField(NodeFieldScope.custom)
  globalRegion?: string;

  @entityRelation({
    getTargetNode: () => Asset,
    backref: 'location',
    scope: NodeFieldScope.custom
  })
  assets?: RelationToMany<Asset>;
}
