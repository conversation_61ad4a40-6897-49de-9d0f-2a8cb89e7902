export const getEdgeReplacementQuery = (criteria) => {
    const query = `mutation replaceAndRetainEdgeNode($options: ReplaceEdgeNodeOptions!) {
        replaceAndRetainEdgeNode(options: $options) {
            bindingPlatforms
            createdAt
            deviceTableId
            edgeId
            edgeNodeId
            externalId
            groupId
            topicPart
        }
    }`;

    const variables = {
        options: {
            groupName: criteria.groupName,
            oldEdgeNodeId: criteria.oldEdgeNodeId,
            newEdgeNodeId: criteria.newEdgeNodeId,
            deviceIds: criteria.assetSerialNumber
        },
    }
    return { query, variables }
}