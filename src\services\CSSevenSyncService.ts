import { BaseService, SdkNodeResponse } from "@carrier-io/backend-lib-node-sdk";
import { Asset } from "../entities/Asset";
import { Location } from "../entities/Location";
import { Customer } from "../entities/Customer";
import { Edge } from "../entities/Edge";
import { DynamoDBService } from "./dynamo-db-service";
import { DeviceView } from "../entities/DeviceView";
import { SiteView } from "../entities/SiteView";
import { logger } from '../utils/logger';
import * as AWS from "aws-sdk";

import {
  customerUpdateParams,
  locationUpdateParams,
  assetUpdateParams,
  assetFulfilledAtUpdateParams,
  deviceViewUpsertParams,
  siteViewUpsertParams,
  edgeUpsertParams,
} from "../utils/mappings";
const assetUpsertPreHook = process.env.ASSET_UPSERT_PRE_HOOK || "";
const edgeUpsertPreHook = process.env.EDGE_UPSERT_PRE_HOOK || "";

export class CSSevenSyncService {
  private static assetService: BaseService<Asset> = new BaseService<Asset>(
    Asset
  );
  private static customerService: BaseService<Customer> =
    new BaseService<Customer>(Customer);
  private static locationService: BaseService<Location> =
    new BaseService<Location>(Location);
  private static edgeService: BaseService<Edge> = new BaseService<Edge>(Edge);
  private static deviceViewService: BaseService<DeviceView> =
    new BaseService<DeviceView>(DeviceView);
  private static siteViewService: BaseService<SiteView> =
    new BaseService<SiteView>(SiteView);

  public static async processEvent(event: any, instance: string): Promise<any> {
    const parsedEvent = JSON.parse(event).detail;
    logger.info("parsed event : ", parsedEvent);
    const dynamoDBService: DynamoDBService = new DynamoDBService();
    try {
      const edgeRecord = await this.lookupEdge(parsedEvent, instance);

      const [customerRecord, locationRecord] = await Promise.all([
        this.customerService.upsert(
          customerUpdateParams([parsedEvent.customer])
        ),
        this.locationService.upsert(
          locationUpdateParams([parsedEvent.location])
        ),
      ]);

      logger.info(
        `location data updated for locationId ${locationRecord[0].node.locationId}`,
        locationRecord
      );
      logger.info(
        `customer data updated for customerId ${customerRecord[0].node.customerId}`,
        customerRecord
      );
      if (parsedEvent.assets.length > 0) {

        const assetRecord = await this.assetService.upsert(
          assetUpdateParams(
            parsedEvent.assets,
            customerRecord[0].node,
            edgeRecord[0],
            locationRecord[0].node
          ),
          {
            preHook: [assetUpsertPreHook],
          }
        );
        logger.debug(
          `asset data updated for assetId ${assetRecord[0].node.assetId}`,
          assetRecord
        );

        await Promise.all(
          assetRecord.map(async (asset: any) => {
            if (asset.isSuccess && asset?.node?.id) {
              if (
                asset.node.isCommissioned &&
                asset.node.isOnboarded &&
                (!asset.node.fulfilledAt || asset.node.fulfilledAt == 0)
              ) {
                const response = await this.assetService.upsert(
                  assetFulfilledAtUpdateParams(
                    parsedEvent.assets,
                    edgeRecord[0].edgeId,
                    Date.now()
                  ),
                  {
                    preHook: [assetUpsertPreHook],
                  }
                );

                if (response[0].isSuccess && response[0]?.node?.id) {
                  logger.info(
                    `asset ${response[0].node.id} fulfilledAt value updated successfully`
                  );
                } else {
                  logger.error(
                    `failed to update asset ${response[0].node.id} fulfilledAt value`
                  );
                }
              }
            }
          })
        );
        // //#region design data update
        // await Promise.all(
        //   assetRecord.map(async (asset) => {
        //     if (
        //       asset?.node?.designData &&
        //       !this.isObjectEmpty(asset?.node?.designData)
        //     ) {
        //       await dynamoDBService.upsertDesignData(
        //         asset.node,
        //         edgeRecord[0].serialNumber
        //       );
        //     }
        //   })
        // );
        // console.log("case details updated successfully");
        //#endregion
        const assetList = assetRecord.map((item) => item.node);

        const deviceViewRecord = await this.deviceViewService.upsert(
          deviceViewUpsertParams(
            assetList,
            locationRecord[0].node,
            customerRecord[0].node,
            edgeRecord[0]
          )
        );
        logger.info(
          `deviceview entity is upserted ${JSON.stringify(deviceViewRecord)}`
        );

        const siteInfo: Asset[] = await this.assetService.select({
          filters: {
            location: { contains: locationRecord[0].node.id },
          },
          include: ["edges"],
        });
        logger.info(
          `assets associated to location: ${
            locationRecord[0].node.id
          } are: ${JSON.stringify(siteInfo)}`
        );

        const siteViewRecord = await this.siteViewService.upsert(
          siteViewUpsertParams(
            siteInfo,
            locationRecord[0].node,
            customerRecord[0].node
          )
        );
        logger.info(
          `siteview entity is upserted ${JSON.stringify(siteViewRecord)}`
        );
      } else {
        logger.warn("current event has no assests:", parsedEvent.assets.length);
      }
    } catch (err: any) {
      logger.error("saving data is failed", JSON.stringify(err));
      
      return err;
    }
    return true;
  }

  private static async lookupEdge(event: any, instance: string) {
    let edgeRecord: any = [];

    let edgeCreateResponse: SdkNodeResponse<Edge>[] =
      await this.edgeService.upsert(edgeUpsertParams(event, instance), {
        preHook: [edgeUpsertPreHook],
      });

    if (edgeCreateResponse[0].isSuccess) {
      edgeRecord.push(edgeCreateResponse[0].node);
      logger.info("edge upserted in zephyr", edgeRecord);
    } else {
      throw new Error(
        `error while creating edge: ${edgeCreateResponse[0].errorMessage}`
      );
    }

    return edgeRecord;
  }

  private static isObjectEmpty(obj: any): boolean {
    return Object.keys(obj).length === 0 && obj.constructor === Object;
  }
}
