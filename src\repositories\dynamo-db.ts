import * as AWS from 'aws-sdk';
import { IDynamoDB } from './dynamo-db-interface';
import { logger } from '../utils/logger';

export class DynamoDB implements IDynamoDB {

    public async updateItem(params: any): Promise<boolean> {
        const dynamoClient = new AWS.DynamoDB.DocumentClient();
        try {
            await dynamoClient.update(params).promise();
            return true;
        } catch (error: any) {
            logger.error("Error in Dynamo DB Update Item", error);
            throw error;
        }
    }
}
