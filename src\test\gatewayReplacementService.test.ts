import 'reflect-metadata';
import { GatewayReplacementService } from '../services/GatewayReplacementService';
import { GatewayInfoHandlingService } from '../services/GatewayInfoHandlingService';
import { DeviceViewInfoHandlingService } from '../services/DeviceViewInfoHandlingService';
import { LookupGqlService } from '../services/lookup-gql-service';
import { EventHistoryManagementService } from '../services/EventHistoryManagementService';
import { deviceViewMockData } from '../mocks/deviceViewMockDataResponse';
import { mockCreateDeviewResponse } from '../mocks/createDeviceViewsMockResponse';
import { edgeUpsertMock } from '../mocks/edgeupsertResponse';

// Mock all dependencies
jest.mock('../services/GatewayInfoHandlingService');
jest.mock('../services/DeviceViewInfoHandlingService');
jest.mock('../services/lookup-gql-service');
jest.mock('../services/EventHistoryManagementService');

describe('GatewayReplacementService', () => {
  let service: GatewayReplacementService;
  let mockGatewayInfoService: jest.Mocked<GatewayInfoHandlingService>;
  let mockDeviceViewInfoService: jest.Mocked<DeviceViewInfoHandlingService>;
  let mockLookupService: jest.Mocked<LookupGqlService>;
  let mockEventHistoryService: jest.Mocked<EventHistoryManagementService>;

  const mockOldGatewayDetails = [{
    id: 'old-gateway-id',
    model: 'CJC_Eye',
    serialNumber: 'KE1W000310',
    edgeId: '866226060854159'
  }];

  const mockDlsResponse = {
    edgeId: 'new-gateway-node-id',
    success: true
  };

  const validPayload = {
    oldGwid: '866226060854159',
    newGwid: '866226062516863',
    oldGwSerialNumber: 'KE1W000310',
    newGwSerialNumber: 'E1Y25BS4D000019',
    newGatewayModel: 'CJC_Eye_V2',
    replacementType: 'full'
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockGatewayInfoService = new GatewayInfoHandlingService() as jest.Mocked<GatewayInfoHandlingService>;
    mockDeviceViewInfoService = new DeviceViewInfoHandlingService() as jest.Mocked<DeviceViewInfoHandlingService>;
    mockLookupService = new LookupGqlService() as jest.Mocked<LookupGqlService>;
    mockEventHistoryService = new EventHistoryManagementService() as jest.Mocked<EventHistoryManagementService>;

    mockGatewayInfoService.gatewayExists = jest.fn();
    mockGatewayInfoService.getAndUpdateOldGatewayDetails = jest.fn();
    mockDeviceViewInfoService.getDeviceViews = jest.fn();
    mockDeviceViewInfoService.deleteDeviceViews = jest.fn();
    mockDeviceViewInfoService.updateDeviceViews = jest.fn();
    mockLookupService.replaceAndRetainEdgeNode = jest.fn();
    mockEventHistoryService.addGatewayReplacementHistory = jest.fn();
    mockEventHistoryService.addAssetHistory = jest.fn();

    service = new GatewayReplacementService();
    (service as any).gatewayInfoService = mockGatewayInfoService;
    (service as any).deviceViewInfoService = mockDeviceViewInfoService;
    (service as any).lookupService = mockLookupService;
    (service as any).eventHistoryManagementService = mockEventHistoryService;
  });

  describe('Constructor', () => {
    it('should initialize all services', () => {
      const newService = new GatewayReplacementService();
      
      expect(newService).toBeInstanceOf(GatewayReplacementService);
      expect((newService as any).lookupService).toBeDefined();
      expect((newService as any).gatewayInfoService).toBeDefined();
      expect((newService as any).deviceViewInfoService).toBeDefined();
      expect((newService as any).eventHistoryManagementService).toBeDefined();
    });
  });

  describe('updateDeviceLookup method', () => {
    it('should call replaceAndRetainEdgeNode with correct parameters', async () => {
      mockLookupService.replaceAndRetainEdgeNode.mockResolvedValue({ edgeId: 'new-edge-id' });

      const result = await service.updateDeviceLookup('oldEdge', 'newEdge', ['asset1']);

      expect(mockLookupService.replaceAndRetainEdgeNode).toHaveBeenCalledWith({
        groupName: 'ahp',
        oldEdgeNodeId: 'oldEdge',
        newEdgeNodeId: 'newEdge',
        assetSerialNumber: ['asset1']
      });
      expect(result).toEqual({ edgeId: 'new-edge-id' });
    });

    it('should handle empty asset serial number array', async () => {
      mockLookupService.replaceAndRetainEdgeNode.mockResolvedValue(mockDlsResponse);

      const result = await service.updateDeviceLookup('oldEdge', 'newEdge', []);

      expect(mockLookupService.replaceAndRetainEdgeNode).toHaveBeenCalledWith({
        groupName: 'ahp',
        oldEdgeNodeId: 'oldEdge',
        newEdgeNodeId: 'newEdge',
        assetSerialNumber: []
      });
      expect(result).toEqual(mockDlsResponse);
    });

    it('should throw error when DLS API fails', async () => {
      mockLookupService.replaceAndRetainEdgeNode.mockRejectedValue(new Error('DLS Error'));

      await expect(service.updateDeviceLookup('oldEdge', 'newEdge', [])).rejects.toThrow('DLS API update failed');
    });
  });

  describe('handleGatewayReplacement - Success Scenarios', () => {
    it('should successfully process full gateway replacement', async () => {
      mockGatewayInfoService.gatewayExists.mockResolvedValue(mockOldGatewayDetails);
      mockLookupService.replaceAndRetainEdgeNode.mockResolvedValue(mockDlsResponse);
      mockGatewayInfoService.getAndUpdateOldGatewayDetails.mockResolvedValue(1);
      mockDeviceViewInfoService.getDeviceViews.mockResolvedValue(deviceViewMockData as any);
      mockDeviceViewInfoService.deleteDeviceViews.mockResolvedValue(undefined);
      mockDeviceViewInfoService.updateDeviceViews.mockResolvedValue(4);
      mockEventHistoryService.addGatewayReplacementHistory.mockResolvedValue(true);
      mockEventHistoryService.addAssetHistory.mockResolvedValue(undefined);

      const result = await service.handleGatewayReplacement(validPayload);

      expect(result.success).toBe(true);
      expect(result.message).toContain('Successfully processed gateway replacement');
      expect(result.timestamp).toBeDefined();
      expect(mockGatewayInfoService.gatewayExists).toHaveBeenCalledWith('866226060854159', 'KE1W000310');
      expect(mockLookupService.replaceAndRetainEdgeNode).toHaveBeenCalled();
      expect(mockGatewayInfoService.getAndUpdateOldGatewayDetails).toHaveBeenCalled();
      expect(mockDeviceViewInfoService.getDeviceViews).toHaveBeenCalled();
      expect(mockDeviceViewInfoService.updateDeviceViews).toHaveBeenCalled();
      expect(mockEventHistoryService.addGatewayReplacementHistory).toHaveBeenCalled();
      expect(mockEventHistoryService.addAssetHistory).toHaveBeenCalled();
    });

    it('should handle partial replacement type', async () => {
      const partialPayload = { ...validPayload, replacementType: 'partial' };
      
      mockGatewayInfoService.gatewayExists.mockResolvedValue(mockOldGatewayDetails);
      mockLookupService.replaceAndRetainEdgeNode.mockResolvedValue(mockDlsResponse);
      mockGatewayInfoService.getAndUpdateOldGatewayDetails.mockResolvedValue(1);
      mockDeviceViewInfoService.getDeviceViews.mockResolvedValue([]);
      mockEventHistoryService.addGatewayReplacementHistory.mockResolvedValue(true);

      const result = await service.handleGatewayReplacement(partialPayload);

      expect(result.success).toBe(true);
      expect(mockEventHistoryService.addGatewayReplacementHistory).toHaveBeenCalledWith(
        expect.objectContaining({ replacementType: 'partial' })
      );
    });

    it('should handle case with no existing device views', async () => {
      mockGatewayInfoService.gatewayExists.mockResolvedValue(mockOldGatewayDetails);
      mockLookupService.replaceAndRetainEdgeNode.mockResolvedValue(mockDlsResponse);
      mockGatewayInfoService.getAndUpdateOldGatewayDetails.mockResolvedValue(1);
      mockDeviceViewInfoService.getDeviceViews.mockResolvedValue([]);
      mockEventHistoryService.addGatewayReplacementHistory.mockResolvedValue(true);

      const result = await service.handleGatewayReplacement(validPayload);

      expect(result.success).toBe(true);
      expect(mockDeviceViewInfoService.deleteDeviceViews).not.toHaveBeenCalled();
      expect(mockDeviceViewInfoService.updateDeviceViews).not.toHaveBeenCalled();
      expect(mockEventHistoryService.addAssetHistory).not.toHaveBeenCalled();
    });

    it('should continue processing when device view deletion fails', async () => {
      mockGatewayInfoService.gatewayExists.mockResolvedValue(mockOldGatewayDetails);
      mockLookupService.replaceAndRetainEdgeNode.mockResolvedValue(mockDlsResponse);
      mockGatewayInfoService.getAndUpdateOldGatewayDetails.mockResolvedValue(1);
      mockDeviceViewInfoService.getDeviceViews.mockResolvedValue(deviceViewMockData as any);
      mockDeviceViewInfoService.deleteDeviceViews.mockRejectedValue(new Error('Delete failed'));
      mockDeviceViewInfoService.updateDeviceViews.mockResolvedValue(4);
      mockEventHistoryService.addGatewayReplacementHistory.mockResolvedValue(true);
      mockEventHistoryService.addAssetHistory.mockResolvedValue(undefined);

      const result = await service.handleGatewayReplacement(validPayload);

      expect(result.success).toBe(true);
      expect(mockDeviceViewInfoService.updateDeviceViews).toHaveBeenCalled();
    });
  });

  describe('handleGatewayReplacement - Validation', () => {
    it('should validate required fields', async () => {
      const invalidPayload = {
        oldGwid: '',
        newGwid: '866226062516863',
        oldGwSerialNumber: 'KE1W000310',
        newGwSerialNumber: 'E1Y25BS4D000019',
        newGatewayModel: 'CJC_Eye_V2',
        replacementType: 'full'
      };

      const result = await service.handleGatewayReplacement(invalidPayload);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Missing required fields');
    });

    it('should validate all required fields individually', async () => {
      const requiredFields = ['oldGwid', 'newGwid', 'oldGwSerialNumber', 'newGwSerialNumber', 'newGatewayModel', 'replacementType'];
      const basePayload = {
        oldGwid: '866226060854159',
        newGwid: '866226062516863',
        oldGwSerialNumber: 'KE1W000310',
        newGwSerialNumber: 'E1Y25BS4D000019',
        newGatewayModel: 'CJC_Eye_V2',
        replacementType: 'full'
      };

      for (const field of requiredFields) {
        const invalidPayload = { ...basePayload, [field]: '' };
        const result = await service.handleGatewayReplacement(invalidPayload);
        
        expect(result.success).toBe(false);
        expect(result.error).toContain('Missing required fields');
      }
    });
  });

  describe('handleGatewayReplacement - Error Scenarios', () => {
    it('should fail when old gateway not found', async () => {
      mockGatewayInfoService.gatewayExists.mockResolvedValue(null);

      const result = await service.handleGatewayReplacement(validPayload);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Old gateway details not found');
    });

    it('should fail when DLS update fails', async () => {
      mockGatewayInfoService.gatewayExists.mockResolvedValue(mockOldGatewayDetails);
      mockLookupService.replaceAndRetainEdgeNode.mockResolvedValue(null);

      const result = await service.handleGatewayReplacement(validPayload);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to process gateway replacement');
    });

    it('should handle DLS response without edgeId', async () => {
      mockGatewayInfoService.gatewayExists.mockResolvedValue([{ id: 'test', model: 'test' }]);
      mockLookupService.replaceAndRetainEdgeNode.mockResolvedValue({ success: true });

      const result = await service.handleGatewayReplacement(validPayload);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to process gateway replacement');
    });

    it('should fail when old gateway update fails', async () => {
      mockGatewayInfoService.gatewayExists.mockResolvedValue(mockOldGatewayDetails);
      mockLookupService.replaceAndRetainEdgeNode.mockResolvedValue(mockDlsResponse);
      mockGatewayInfoService.getAndUpdateOldGatewayDetails.mockResolvedValue(null);

      const result = await service.handleGatewayReplacement(validPayload);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to Update Old gateway');
    });

    it('should handle service exceptions gracefully', async () => {
      mockGatewayInfoService.gatewayExists.mockRejectedValue(new Error('Service error'));

      const result = await service.handleGatewayReplacement(validPayload);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to process gateway replacement');
      expect(result.error).toContain('Service error');
    });

    it('should handle empty gateway details array', async () => {
      mockGatewayInfoService.gatewayExists.mockResolvedValue([]);

      const result = await service.handleGatewayReplacement(validPayload);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Old gateway details not found');
    });

    it('should handle gateway details without id', async () => {
      mockGatewayInfoService.gatewayExists.mockResolvedValue([{ model: 'test' }]);

      const result = await service.handleGatewayReplacement(validPayload);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Old gateway details not found');
    });

    it('should handle device view processing errors', async () => {
      mockGatewayInfoService.gatewayExists.mockResolvedValue([{ id: 'test', model: 'test' }]);
      mockLookupService.replaceAndRetainEdgeNode.mockResolvedValue({ edgeId: 'new-id' });
      mockGatewayInfoService.getAndUpdateOldGatewayDetails.mockResolvedValue(1);
      mockDeviceViewInfoService.getDeviceViews.mockRejectedValue(new Error('Device view error'));

      const result = await service.handleGatewayReplacement(validPayload);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Device view error');
    });

    it('should handle event history errors', async () => {
      mockGatewayInfoService.gatewayExists.mockResolvedValue([{ id: 'test', model: 'test' }]);
      mockLookupService.replaceAndRetainEdgeNode.mockResolvedValue({ edgeId: 'new-id' });
      mockGatewayInfoService.getAndUpdateOldGatewayDetails.mockResolvedValue(1);
      mockDeviceViewInfoService.getDeviceViews.mockResolvedValue([]);
      mockEventHistoryService.addGatewayReplacementHistory.mockRejectedValue(new Error('History error'));

      const result = await service.handleGatewayReplacement(validPayload);

      expect(result.success).toBe(false);
      expect(result.error).toContain('History error');
    });
  });

  describe('Response Format Validation', () => {
    it('should return consistent success response format', async () => {
      mockGatewayInfoService.gatewayExists.mockResolvedValue(mockOldGatewayDetails);
      mockLookupService.replaceAndRetainEdgeNode.mockResolvedValue(mockDlsResponse);
      mockGatewayInfoService.getAndUpdateOldGatewayDetails.mockResolvedValue(1);
      mockDeviceViewInfoService.getDeviceViews.mockResolvedValue([]);
      mockEventHistoryService.addGatewayReplacementHistory.mockResolvedValue(true);

      const result = await service.handleGatewayReplacement(validPayload);

      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('message');
      expect(result).toHaveProperty('timestamp');
      expect(typeof result.success).toBe('boolean');
      expect(typeof result.message).toBe('string');
      expect(typeof result.timestamp).toBe('string');
    });

    it('should return consistent error response format', async () => {
      const invalidPayload = {
        oldGwid: '',
        newGwid: '866226062516863',
        oldGwSerialNumber: 'KE1W000310',
        newGwSerialNumber: 'E1Y25BS4D000019',
        newGatewayModel: 'CJC_Eye_V2',
        replacementType: 'full'
      };

      const result = await service.handleGatewayReplacement(invalidPayload);

      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('error');
      expect(result).toHaveProperty('timestamp');
      expect(typeof result.success).toBe('boolean');
      expect(typeof result.error).toBe('string');
      expect(typeof result.timestamp).toBe('string');
      expect(result.success).toBe(false);
    });
  });

  describe('Mock Response Integration', () => {
    it('should work with deviceViewMockData', async () => {
      mockGatewayInfoService.gatewayExists.mockResolvedValue(mockOldGatewayDetails);
      mockLookupService.replaceAndRetainEdgeNode.mockResolvedValue(mockDlsResponse);
      mockGatewayInfoService.getAndUpdateOldGatewayDetails.mockResolvedValue(1);
      mockDeviceViewInfoService.getDeviceViews.mockResolvedValue(deviceViewMockData as any);
      mockDeviceViewInfoService.deleteDeviceViews.mockResolvedValue(undefined);
      mockDeviceViewInfoService.updateDeviceViews.mockResolvedValue(deviceViewMockData.length);
      mockEventHistoryService.addGatewayReplacementHistory.mockResolvedValue(true);
      mockEventHistoryService.addAssetHistory.mockResolvedValue(undefined);

      const result = await service.handleGatewayReplacement(validPayload);

      expect(result.success).toBe(true);
      expect(mockDeviceViewInfoService.getDeviceViews).toHaveBeenCalled();
      expect(mockDeviceViewInfoService.updateDeviceViews).toHaveBeenCalledWith(
        deviceViewMockData,
        'new-gateway-node-id',
        'E1Y25BS4D000019',
        '866226062516863'
      );
    });

    it('should validate mock response structures', () => {
      expect(Array.isArray(deviceViewMockData)).toBe(true);
      expect(deviceViewMockData.length).toBeGreaterThan(0);
      
      const firstDeviceView = deviceViewMockData[0];
      expect(firstDeviceView).toHaveProperty('id');
      expect(firstDeviceView).toHaveProperty('domain');
      expect(firstDeviceView).toHaveProperty('assetNodeId');
      expect(firstDeviceView).toHaveProperty('edgeNodeId');

      expect(Array.isArray(mockCreateDeviewResponse)).toBe(true);
      expect(mockCreateDeviewResponse.length).toBeGreaterThan(0);
      
      const firstCreateResponse = mockCreateDeviewResponse[0];
      expect(firstCreateResponse).toHaveProperty('isSuccess');
      expect(firstCreateResponse).toHaveProperty('node');
      expect(firstCreateResponse.isSuccess).toBe(true);

      expect(Array.isArray(edgeUpsertMock)).toBe(true);
      expect(edgeUpsertMock.length).toBeGreaterThan(0);
      
      const firstEdgeUpsert = edgeUpsertMock[0];
      expect(firstEdgeUpsert).toHaveProperty('isSuccess');
      expect(firstEdgeUpsert).toHaveProperty('node');
      expect(firstEdgeUpsert.isSuccess).toBe(true);
      expect(firstEdgeUpsert.node).toHaveProperty('isEdgeReplaced');
      expect(firstEdgeUpsert.node.isEdgeReplaced).toBe(true);
    });
  });
});