export class EventHistoryService {
  
  /**
   * Adds gateway replacement history event
   * @param oldGatewayId The old gateway ID
   * @param newGatewayId The new gateway ID
   * @param oldSerialNumber The old gateway serial number
   * @param newSerialNumber The new gateway serial number
   */
  public async addGatewayReplacementHistory(
    oldGatewayId: string,
    newGatewayId: string,
    oldSerialNumber: string,
    newSerialNumber: string
  ): Promise<void> {
    try {
      console.log(`Recording gateway replacement history: ${oldGatewayId} -> ${newGatewayId}`);
      // Implementation for recording gateway replacement history
    } catch (err) {
      console.log("Error adding gateway replacement history:", err);
    }
  }
}