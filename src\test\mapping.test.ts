import { deviceViewUpsertParams, siteViewUpsertParams } from '../../src/utils/mappings';
afterEach(() => {
  jest.clearAllMocks();
});

describe('Test device view upsert params', () => {
  it('Device View upsert UC is Attached to MC', async () => {
    let response = await deviceViewUpsertParams(assetRequestParameters1, locationRequest, customerRequest, edgeRequest);
    expect(response.length).toBe(2);
    expect(response[0].chillerCount).toBe(1);
  });
  it('Device View upsert UC is Attached to GC', async () => {
    let response = await deviceViewUpsertParams(assetRequestParameters2, locationRequest, customerRequest, edgeRequest);
    expect(response.length).toBe(2);
    expect(response[0].chillerCount).toBe(1);
  });
  it('Device View upsert UC', async () => {
    let response = await deviceViewUpsertParams(assetRequestParameters3, locationRequest, customerRequest, edgeRequest);
    expect(response.length).toBe(1);
    expect(response[0].chillerCount).toBe(0);
  });
  it('Device View upsert MC', async () => {
    let response = await deviceViewUpsertParams(assetRequestParameters4, locationRequest, customerRequest, edgeRequest);
    expect(response.length).toBe(1);
    expect(response[0].chillerCount).toBe(0);
  });
  it('Device View upsert GC', async () => {
    let response = await deviceViewUpsertParams(assetRequestParameters5, locationRequest, customerRequest, edgeRequest);
    expect(response.length).toBe(1);
    expect(response[0].chillerCount).toBe(0);
  });
});

describe('Site View Upsert Param', () => {
  it('should count masterControllerCount and unitControllerCount properly', async () => {
    const response = await siteViewUpsertParams(assetRequestParameters1, locationRequest, customerRequest);
    expect(response[0].masterControllerCount).toEqual(1);
    expect(response[0].unitControllerCount).toEqual(1);
  });

  it('should return count value as 0, when there are no masterController or unitController', async () => {
    const response = await siteViewUpsertParams(assetRequestParameters2, locationRequest, customerRequest);
    expect(response[0].masterControllerCount).toEqual(0);
  });
});

const assetRequestParameters1: any = [
  {
    warrantyType: null,
    warrantyEndDate: null,
    stdwarrantyType: null,
    stdwarrantyExpDate: null,
    serialNumber: 'RUAUP511HM9999999980',
    productType: 'チリングユニット',
    modelNumber: 'RUA-UP511HM',
    model: 'USXEDGE32',
    address: '00_01_00',
    ctrlType: 'MC',
    extendedWarrantyExpDate: null,
    designData: {
      std_design_voltage: null,
      std_design_startervfd: null,
      std_design_refrigerantcharge: null,
      std_design_refrigerant: null,
      std_design_kwton: null,
      std_design_kw: null,
      std_design_fullload: null,
      std_design_evapwaterbox: null,
      std_design_evaptubing: null,
      std_design_evaptubecount: null,
      std_design_evappd: null,
      std_design_evaplwt: null,
      std_design_evapheadweight: null,
      std_design_evapheadrigging: null,
      std_design_evapflowrate: null,
      std_design_evapewt: null,
      std_design_condwaterboxtype: null,
      std_design_condtubing: null,
      std_design_condtubecount: null,
      std_design_condpd: null,
      std_design_condlwt: null,
      std_design_condheadweight: null,
      std_design_condheadrigging: null,
      std_design_condflowrate: null,
      std_design_condewt: null,
      std_design_capacity: null,
    },
    designationName: '9999999980',
    ContractStartDate: null,
    ContractNameNumber: null,
    ContractJobType: null,
    contractEndDate: null,
    deviceId: 'RUAUP511HM9999999980',
    assetId: 'CM240000104',
    area: 'e-THIRD_test',
    id: 'spBv1.0/zephyr/DBIRTH/BMSIFRM1280U40789991/RUAUP511HM9999999980',
    name: 'RUAUP511HM9999999980',
    changedBy: 'CSSeven',
    changedAt: '2024-09-19T11:57:42.308Z',
    cjcserialNumber: '9999999980',
    customer: { operation: 'add', ids: ['d656809f-5591-5691-bb7d-4080092ba887'] },
    edges: [{ id: 'sample_id' }],
    location: { operation: 'add', ids: ['b6d80b43-4c31-51c5-977d-ebc88067b981'] },
    isOnboarded: true,
    warrantyExpDate: null,
    contractId: null,
    contractType: null,
  },
  {
    warrantyType: null,
    warrantyEndDate: null,
    stdwarrantyType: null,
    stdwarrantyExpDate: null,
    serialNumber: 'RUAUP511HM9999999912',
    productType: 'チリングユニット',
    modelNumber: 'RUA-UP511HM',
    model: 'USXEDGE32',
    address: '00_01_01',
    ctrlType: 'UC',
    extendedWarrantyExpDate: null,
    designData: {
      std_design_voltage: null,
      std_design_startervfd: null,
      std_design_refrigerantcharge: null,
      std_design_refrigerant: null,
      std_design_kwton: null,
      std_design_kw: null,
      std_design_fullload: null,
      std_design_evapwaterbox: null,
      std_design_evaptubing: null,
      std_design_evaptubecount: null,
      std_design_evappd: null,
      std_design_evaplwt: null,
      std_design_evapheadweight: null,
      std_design_evapheadrigging: null,
      std_design_evapflowrate: null,
      std_design_evapewt: null,
      std_design_condwaterboxtype: null,
      std_design_condtubing: null,
      std_design_condtubecount: null,
      std_design_condpd: null,
      std_design_condlwt: null,
      std_design_condheadweight: null,
      std_design_condheadrigging: null,
      std_design_condflowrate: null,
      std_design_condewt: null,
      std_design_capacity: null,
    },
    designationName: '9999999912',
    ContractStartDate: null,
    ContractNameNumber: null,
    ContractJobType: null,
    contractEndDate: null,
    deviceId: 'RUAUP511HM9999999912',
    assetId: 'CM240000112',
    area: 'e-THIRD_test',
    id: 'spBv1.0/zephyr/DBIRTH/BMSIFRM1280U40789991/RUAUP511HM9999999912',
    name: 'RUAUP511HM9999999912',
    changedBy: 'CSSeven',
    changedAt: '2024-09-19T11:57:42.308Z',
    cjcserialNumber: '9999999912',
    customer: { operation: 'add', ids: ['d656809f-5591-5691-bb7d-4080092ba887'] },
    edges: [{ id: 'sample_id' }],
    location: { operation: 'add', ids: ['b6d80b43-4c31-51c5-977d-ebc88067b981'] },
    isOnboarded: true,
    warrantyExpDate: null,
    contractId: null,
    contractType: null,
  },
];

const assetRequestParameters2: any = [
  {
    warrantyType: null,
    warrantyEndDate: null,
    stdwarrantyType: null,
    stdwarrantyExpDate: null,
    serialNumber: 'RUAUP511HM9999999911',
    productType: 'チリングユニット',
    modelNumber: 'RUA-UP511HM',
    model: 'USXEDGE32',
    address: '01_00_00',
    ctrlType: 'GC',
    extendedWarrantyExpDate: null,
    designData: {
      std_design_voltage: null,
      std_design_startervfd: null,
      std_design_refrigerantcharge: null,
      std_design_refrigerant: null,
      std_design_kwton: null,
      std_design_kw: null,
      std_design_fullload: null,
      std_design_evapwaterbox: null,
      std_design_evaptubing: null,
      std_design_evaptubecount: null,
      std_design_evappd: null,
      std_design_evaplwt: null,
      std_design_evapheadweight: null,
      std_design_evapheadrigging: null,
      std_design_evapflowrate: null,
      std_design_evapewt: null,
      std_design_condwaterboxtype: null,
      std_design_condtubing: null,
      std_design_condtubecount: null,
      std_design_condpd: null,
      std_design_condlwt: null,
      std_design_condheadweight: null,
      std_design_condheadrigging: null,
      std_design_condflowrate: null,
      std_design_condewt: null,
      std_design_capacity: null,
    },
    designationName: '9999999911',
    ContractStartDate: null,
    ContractNameNumber: null,
    ContractJobType: null,
    contractEndDate: null,
    deviceId: 'RUAUP511HM9999999911',
    assetId: 'CM240000111',
    area: 'e-THIRD_test',
    id: 'spBv1.0/zephyr/DBIRTH/BMSIFRM1280U40789991/RUAUP511HM9999999911',
    name: 'RUAUP511HM9999999911',
    changedBy: 'CSSeven',
    changedAt: '2024-09-19T11:57:42.308Z',
    cjcserialNumber: '9999999911',
    customer: { operation: 'add', ids: ['d656809f-5591-5691-bb7d-4080092ba887'] },
    edges: [{ id: 'sample_id' }],
    location: { operation: 'add', ids: ['b6d80b43-4c31-51c5-977d-ebc88067b981'] },
    isOnboarded: true,
    warrantyExpDate: null,
    contractId: null,
    contractType: null,
  },
  {
    warrantyType: null,
    warrantyEndDate: null,
    stdwarrantyType: null,
    stdwarrantyExpDate: null,
    serialNumber: 'RUAUP511HM9999999912',
    productType: 'チリングユニット',
    modelNumber: 'RUA-UP511HM',
    model: 'USXEDGE32',
    address: '01_00_01',
    ctrlType: 'UC',
    extendedWarrantyExpDate: null,
    designData: {
      std_design_voltage: null,
      std_design_startervfd: null,
      std_design_refrigerantcharge: null,
      std_design_refrigerant: null,
      std_design_kwton: null,
      std_design_kw: null,
      std_design_fullload: null,
      std_design_evapwaterbox: null,
      std_design_evaptubing: null,
      std_design_evaptubecount: null,
      std_design_evappd: null,
      std_design_evaplwt: null,
      std_design_evapheadweight: null,
      std_design_evapheadrigging: null,
      std_design_evapflowrate: null,
      std_design_evapewt: null,
      std_design_condwaterboxtype: null,
      std_design_condtubing: null,
      std_design_condtubecount: null,
      std_design_condpd: null,
      std_design_condlwt: null,
      std_design_condheadweight: null,
      std_design_condheadrigging: null,
      std_design_condflowrate: null,
      std_design_condewt: null,
      std_design_capacity: null,
    },
    designationName: '9999999912',
    ContractStartDate: null,
    ContractNameNumber: null,
    ContractJobType: null,
    contractEndDate: null,
    deviceId: 'RUAUP511HM9999999912',
    assetId: 'CM240000112',
    area: 'e-THIRD_test',
    id: 'spBv1.0/zephyr/DBIRTH/BMSIFRM1280U40789991/RUAUP511HM9999999912',
    name: 'RUAUP511HM9999999912',
    changedBy: 'CSSeven',
    changedAt: '2024-09-19T11:57:42.308Z',
    cjcserialNumber: '9999999912',
    customer: { operation: 'add', ids: ['d656809f-5591-5691-bb7d-4080092ba887'] },
    edges: [{ id: 'sample_id' }],
    location: { operation: 'add', ids: ['b6d80b43-4c31-51c5-977d-ebc88067b981'] },
    isOnboarded: true,
    warrantyExpDate: null,
    contractId: null,
    contractType: null,
  },
];

const assetRequestParameters3: any = [
  {
    warrantyType: null,
    warrantyEndDate: null,
    stdwarrantyType: null,
    stdwarrantyExpDate: null,
    serialNumber: 'RUAUP511HM9999999912',
    productType: 'チリングユニット',
    modelNumber: 'RUA-UP511HM',
    model: 'USXEDGE32',
    address: '00_00_01',
    ctrlType: 'UC',
    extendedWarrantyExpDate: null,
    designData: {
      std_design_voltage: null,
      std_design_startervfd: null,
      std_design_refrigerantcharge: null,
      std_design_refrigerant: null,
      std_design_kwton: null,
      std_design_kw: null,
      std_design_fullload: null,
      std_design_evapwaterbox: null,
      std_design_evaptubing: null,
      std_design_evaptubecount: null,
      std_design_evappd: null,
      std_design_evaplwt: null,
      std_design_evapheadweight: null,
      std_design_evapheadrigging: null,
      std_design_evapflowrate: null,
      std_design_evapewt: null,
      std_design_condwaterboxtype: null,
      std_design_condtubing: null,
      std_design_condtubecount: null,
      std_design_condpd: null,
      std_design_condlwt: null,
      std_design_condheadweight: null,
      std_design_condheadrigging: null,
      std_design_condflowrate: null,
      std_design_condewt: null,
      std_design_capacity: null,
    },
    designationName: '9999999912',
    ContractStartDate: null,
    ContractNameNumber: null,
    ContractJobType: null,
    contractEndDate: null,
    deviceId: 'RUAUP511HM9999999912',
    assetId: 'CM240000112',
    area: 'e-THIRD_test',
    id: 'spBv1.0/zephyr/DBIRTH/BMSIFRM1280U40789991/RUAUP511HM9999999912',
    name: 'RUAUP511HM9999999912',
    changedBy: 'CSSeven',
    changedAt: '2024-09-19T11:57:42.308Z',
    cjcserialNumber: '9999999912',
    customer: { operation: 'add', ids: ['d656809f-5591-5691-bb7d-4080092ba887'] },
    edges: { operation: 'add', ids: ['93602227-ccef-456f-bf1f-de379148bc5e'] },
    location: { operation: 'add', ids: ['b6d80b43-4c31-51c5-977d-ebc88067b981'] },
    isOnboarded: true,
    warrantyExpDate: null,
    contractId: null,
    contractType: null,
  },
];

const assetRequestParameters4: any = [
  {
    warrantyType: null,
    warrantyEndDate: null,
    stdwarrantyType: null,
    stdwarrantyExpDate: null,
    serialNumber: 'RUAUP511HM9999999980',
    productType: 'チリングユニット',
    modelNumber: 'RUA-UP511HM',
    model: 'USXEDGE32',
    address: '00_01_00',
    ctrlType: 'MC',
    extendedWarrantyExpDate: null,
    designData: {
      std_design_voltage: null,
      std_design_startervfd: null,
      std_design_refrigerantcharge: null,
      std_design_refrigerant: null,
      std_design_kwton: null,
      std_design_kw: null,
      std_design_fullload: null,
      std_design_evapwaterbox: null,
      std_design_evaptubing: null,
      std_design_evaptubecount: null,
      std_design_evappd: null,
      std_design_evaplwt: null,
      std_design_evapheadweight: null,
      std_design_evapheadrigging: null,
      std_design_evapflowrate: null,
      std_design_evapewt: null,
      std_design_condwaterboxtype: null,
      std_design_condtubing: null,
      std_design_condtubecount: null,
      std_design_condpd: null,
      std_design_condlwt: null,
      std_design_condheadweight: null,
      std_design_condheadrigging: null,
      std_design_condflowrate: null,
      std_design_condewt: null,
      std_design_capacity: null,
    },
    designationName: '9999999980',
    ContractStartDate: null,
    ContractNameNumber: null,
    ContractJobType: null,
    contractEndDate: null,
    deviceId: 'RUAUP511HM9999999980',
    assetId: 'CM240000104',
    area: 'e-THIRD_test',
    id: 'spBv1.0/zephyr/DBIRTH/BMSIFRM1280U40789991/RUAUP511HM9999999980',
    name: 'RUAUP511HM9999999980',
    changedBy: 'CSSeven',
    changedAt: '2024-09-19T11:57:42.308Z',
    cjcserialNumber: '9999999980',
    customer: { operation: 'add', ids: ['d656809f-5591-5691-bb7d-4080092ba887'] },
    edges: { operation: 'add', ids: ['93602227-ccef-456f-bf1f-de379148bc5e'] },
    location: { operation: 'add', ids: ['b6d80b43-4c31-51c5-977d-ebc88067b981'] },
    isOnboarded: true,
    warrantyExpDate: null,
    contractId: null,
    contractType: null,
  },
];

const assetRequestParameters5: any = [
  {
    warrantyType: null,
    warrantyEndDate: null,
    stdwarrantyType: null,
    stdwarrantyExpDate: null,
    serialNumber: 'RUAUP511HM9999999911',
    productType: 'チリングユニット',
    modelNumber: 'RUA-UP511HM',
    model: 'USXEDGE32',
    address: '01_00_00',
    ctrlType: 'GC',
    extendedWarrantyExpDate: null,
    designData: {
      std_design_voltage: null,
      std_design_startervfd: null,
      std_design_refrigerantcharge: null,
      std_design_refrigerant: null,
      std_design_kwton: null,
      std_design_kw: null,
      std_design_fullload: null,
      std_design_evapwaterbox: null,
      std_design_evaptubing: null,
      std_design_evaptubecount: null,
      std_design_evappd: null,
      std_design_evaplwt: null,
      std_design_evapheadweight: null,
      std_design_evapheadrigging: null,
      std_design_evapflowrate: null,
      std_design_evapewt: null,
      std_design_condwaterboxtype: null,
      std_design_condtubing: null,
      std_design_condtubecount: null,
      std_design_condpd: null,
      std_design_condlwt: null,
      std_design_condheadweight: null,
      std_design_condheadrigging: null,
      std_design_condflowrate: null,
      std_design_condewt: null,
      std_design_capacity: null,
    },
    designationName: '9999999911',
    ContractStartDate: null,
    ContractNameNumber: null,
    ContractJobType: null,
    contractEndDate: null,
    deviceId: 'RUAUP511HM9999999911',
    assetId: 'CM240000111',
    area: 'e-THIRD_test',
    id: 'spBv1.0/zephyr/DBIRTH/BMSIFRM1280U40789991/RUAUP511HM9999999911',
    name: 'RUAUP511HM9999999911',
    changedBy: 'CSSeven',
    changedAt: '2024-09-19T11:57:42.308Z',
    cjcserialNumber: '9999999911',
    customer: { operation: 'add', ids: ['d656809f-5591-5691-bb7d-4080092ba887'] },
    edges: { operation: 'add', ids: ['93602227-ccef-456f-bf1f-de379148bc5e'] },
    location: { operation: 'add', ids: ['b6d80b43-4c31-51c5-977d-ebc88067b981'] },
    isOnboarded: true,
    warrantyExpDate: null,
    contractId: null,
    contractType: null,
  },
];

const locationRequest: any = {
  domain: 'location',
  id: 'b6d80b43-4c31-51c5-977d-ebc88067b981',
  createdAt: '2024-08-19T00:53:50.800Z',
  createdBy: 'CSSevenSyncLambda-dev-bb28a90b-86bf-4080-a499-be8f8ab3e54e',
  updatedAt: '2024-09-19T11:57:42.204Z',
  updatedBy: 'CSSevenSyncLambda-dev-17963b18-71cd-4ce8-b001-950dec2bd993',
  isDeleted: false,
  name: 'Fuji factory_e-THIRD_test',
  country: 'Japan',
  locationId: 'C2400015',
  zip: '4168521',
  street: '蓼原３３６番地',
  state: '静岡県',
  city: '富士市',
  timezone: 'Asia/Tokyo',
  commercialTerritory: '中部支社',
  market: '静岡県',
  globalRegion: 'Japan',
};

const customerRequest: any = {
  domain: 'customer',
  id: 'd656809f-5591-5691-bb7d-4080092ba887',
  createdAt: '2024-09-19T11:57:42.202Z',
  updatedAt: '2024-09-19T11:57:42.202Z',
  isDeleted: false,
  name: 'Japan Carrier Corporation',
  customerId: null,
  contactName: '',
  contactEmail: '',
};

const edgeRequest: any = {
  domain: 'edge',
  id: '93602227-ccef-456f-bf1f-de379148bc5e',
  createdAt: '2024-08-08T23:27:56.500Z',
  createdBy: 'carrier-io-pipeline-consumer-LifecycleConsumer-lambda-dev',
  updatedAt: '2024-09-19T11:57:41.660Z',
  updatedBy: 'CSSeven',
  isDeleted: false,
  name: '40789991',
  birthNotification: 1726746951000,
  deathNotification: 1726746950934,
  isOnline: true,
  CSSInstance: 'dev',
  serialNumber: '40789991',
  edgeId: 'BMSIFRM1280U40789991',
};
