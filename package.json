{"name": "@ahp/ahp-service-csseven-integration", "version": "0.0.0", "description": "AHP to precess cs7 data recieved from eventbridge to neptuneDB.", "license": "Carrier", "author": "Carrier Digital", "main": "dist/index.js", "scripts": {"build": "rm -rf ./dist && tsc", "check-types": "tsc --noEmit", "lint": "eslint ./infra ./src --ext .ts,.js", "prepare": "husky install", "prepublish": "yarn build", "rc": "./cicd/scripts/rc.sh", "test:integration": "yarn build && jest  --runInBand --coverage --collectCoverageFrom='./src/**' --config ./jest.config.integration.js --passWithNoTests", "test:unit": "yarn build && jest --coverage --collectCoverageFrom='./src/**' --config ./jest.config.unit.js --passWithNoTests", "test:gateway": "yarn build && jest --coverage --testPathPattern='gatewayReplacementService.test' --passWithNoTests", "watch": "tsc -w", "test": "jest --coverage"}, "lint-staged": {"**/*.{json,yml,yaml}": ["prettier --write"], "**/*.{js,ts}": ["eslint --ext .js,.ts --cache --fix"]}, "dependencies": {"@aws-crypto/sha256-js": "^4.0.0", "@aws-sdk/client-sts": "^3.374.0", "@aws-sdk/protocol-http": "^3.374.0", "@aws-sdk/signature-v4": "^3.374.0", "@carrier-io/backend-lib-node-sdk": "^2.45.0", "@carrier-io/pe-lib-otel-logs": "^1.16.2", "@carrier-io/io-lib-node-sdk-models": "^1.3.0", "@carrier/backend-lib-core": "^1.0.5", "@zephyr/backend-lib-infrastructure": "^3.1.0", "node-fetch": "^2.6.7", "reflect-metadata": "^0.2.2", "type-graphql": "^2.0.0-rc.2", "mongoose": "^6.12.0"}, "devDependencies": {"@aws-sdk/client-ssm": "^3.374.0", "@carrier-io/backend-lib-infrastructure": "^4.0.2", "@tsconfig/node16": "^16.1.1", "@tsconfig/node18": "^18.2.2", "@tsconfig/node20": "^20.1.0", "@types/jest": "^27.0.2", "@types/node": "^20.10.0", "@types/node-fetch": "^2.6.4", "@typescript-eslint/eslint-plugin": "^5.41.0", "@typescript-eslint/parser": "^5.41.0", "aws-cdk": "^2.88.0", "aws-cdk-lib": "^2.88.0", "aws-sdk": "^2.1514.0", "constructs": "^10.0.0", "esbuild": "0.25.5", "eslint": "^8.11.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^2.5.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jest": "^26.1.2", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-sonarjs": "^0.12.0", "husky": "^8.0.0", "inquirer": "^8.2.0", "jest": "^29.7.0", "jest-sonar-reporter": "^2.0.0", "lint-staged": "13.1.0", "prettier": "^2.6.0", "source-map-support": "^0.5.20", "ts-jest": "^29.1.2", "ts-jest-resolver": "^2.0.1", "ts-node": "^10.8.1", "typescript": "^5.3.3"}, "jestSonar": {"reportPath": "./coverage", "reportFile": "test-reporter.xml", "indent": 4}, "bin": {}}