import { EventRepository } from '../documentDB/repositories/event.repository';

export class EventHistoryManagementService {
  
  /**
   * Adds gateway replacement history events for both gateway and assets
   */
  public async addGatewayReplacementHistory(params: {
    oldGwSerialNumber: string;
    newGwSerialNumber: string;
    oldGatewayNodeId: string;
    newGatewayNodeId: string;
    oldGatewayModel: string;
    newGatewayModel: string;
    replacementType: string;
  }): Promise<boolean> {
    try {
      const baseData = {
        oldGatewaySN: params.oldGwSerialNumber,
        newGatewaySN: params.newGwSerialNumber,
        oldGatewayId: params.oldGatewayNodeId,
        newGatewayId: params.newGatewayNodeId,
        oldGatewayModel: params.oldGatewayModel,
        newGatewayModel: params.newGatewayModel,
        isFullyReplaced: params.replacementType === "full" ? "yes" : "no"
      };
      
      const gatewayEventsArray = [
        { gatewaySN: params.oldGwSerialNumber, gatewayId: params.oldGatewayNodeId },
        { gatewaySN: params.newGwSerialNumber, gatewayId: params.newGatewayNodeId }
      ].map(gateway => this.prepareGatewayHistoryData({ ...baseData, ...gateway }));

      // Insert history events
      const eventRepository = new EventRepository();
      await eventRepository.insertGatewayHistory(gatewayEventsArray);
      
      return true;
    } catch (err) {
      console.log("Failed to add gateway replacement history:", err);
      throw new Error('Failed to add gateway replacement history');
    }
  }

  public async addAssetHistory(assetInfo: any[]): Promise<void> {
    if (assetInfo.length === 0) return;

    try {
      const assetHistoryEvents = assetInfo.map(asset => 
        this.prepareAssetHistoryData(
          asset.newGatewaySN, 
          asset.newGatewayId, 
          asset.newGatewayModel, 
          asset.oldGatewaySN, 
          asset.oldGatewayId, 
          asset.oldGatewayModel, 
          asset.assetId,
          asset.assetType
        )
      );
      
      const eventRepository = new EventRepository();
      await eventRepository.insertAssetHistory(assetHistoryEvents);
    } catch (err) {
      console.log("Failed to add Asset history:", err);
      throw err;
    }
  }

  private prepareGatewayHistoryData(params: {
    gatewaySN: string;
    gatewayId: string;
    oldGatewaySN: string;
    newGatewaySN: string;
    oldGatewayId: string;
    newGatewayId: string;
    oldGatewayModel: string;
    newGatewayModel: string;
    isFullyReplaced: string;
  }) {
    return {
      timestamp: new Date().getTime(),
      edgeSN: params.gatewaySN,
      edgeId: params.gatewayId,
      name: "Replace",
      type: "DEVICE",
      properties: [
        { key: "oldEdgeSN", value: params.oldGatewaySN },
        { key: "newEdgeSN", value: params.newGatewaySN },
        { key: "oldEdgeId", value: params.oldGatewayId },
        { key: "newEdgeId", value: params.newGatewayId },
        { key: "oldEdgeModel", value: params.oldGatewayModel },
        { key: "newEdgeModel", value: params.newGatewayModel },
        { key: "isFullyReplaced", value: params.isFullyReplaced }
      ]
    };
  }

  private prepareAssetHistoryData(
    newGatewaySN: string, newGatewayId: string, newGatewayModel: string, oldGatewaySN: string, oldGatewayId: string, oldGatewayModel: string, assetId: string, assetType: any) {
    const timestamp = new Date().getTime();

    return {
      createdAt: timestamp,
      startTimestamp: timestamp,
      updatedAt: timestamp,
      action: "EdgeReplace",
      reason: "Gateway replacement",
      actor: {
        cid: 'CS-SevenSyncService',
        name: 'CS-SevenSyncService',
        type: 'System',
      },
      targetEntity: {
        cid: assetId,
        type: assetType
      },
      properties: [
        { key: "oldEdgeSN", value: oldGatewaySN },
        { key: "oldEdgeId", value: oldGatewayId },
        { key: "oldEdgeModel", value: oldGatewayModel },
        { key: "newEdgeSN", value: newGatewaySN },
        { key: "newEdgeId", value: newGatewayId },
        { key: "newEdgeModel", value: newGatewayModel }
      ],
    };
  }
}