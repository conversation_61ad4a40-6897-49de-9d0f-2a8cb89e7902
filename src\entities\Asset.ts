import {
  NodeFieldScope,
  RelationToMany,
  entityField,
  entityName,
  entityRelation,
} from '@carrier-io/backend-lib-node-sdk';
import { BaseAssetEntityClass } from '@carrier-io/io-lib-node-sdk-models';
import { Customer } from './Customer';
import { Location } from './Location';
import { Edge } from './Edge';

@entityName('asset')
export class Asset extends BaseAssetEntityClass {
  @entityField(NodeFieldScope.custom)
  assetId?: string;

  @entityField(NodeFieldScope.custom)
  serialNumber?: string;

  @entityField(NodeFieldScope.custom)
  model?: string;

  @entityField(NodeFieldScope.custom)
  modelNumber?: string;

  @entityField(NodeFieldScope.custom)
  designationName?: string;

  @entityField(NodeFieldScope.custom)
  productType?: string;

  @entityField(NodeFieldScope.custom)
  designData?: object;

  @entityField(NodeFieldScope.custom)
  isOnboarded?: boolean;

  @entityField(NodeFieldScope.custom)
  isCommissioned?: boolean;

  @entityField(NodeFieldScope.custom)
  isOnline?: boolean;

  @entityField(NodeFieldScope.custom)
  cioTags?: string;

  @entityField(NodeFieldScope.custom)
  softwareVersion: string;

  @entityField(NodeFieldScope.custom)
  picControllerVersion?: string;

  @entityField(NodeFieldScope.custom)
  equipmentFamily?: string;

  @entityField(NodeFieldScope.custom)
  fulfilledAt?: number

  @entityField(NodeFieldScope.custom)
  warrantyType?: string

  @entityField(NodeFieldScope.custom)
  warrantyExpDate?: string

  @entityField(NodeFieldScope.custom)
  extendedWarrantyExpDate?: string
  
  @entityField(NodeFieldScope.custom)
  contractId?: string

  @entityField(NodeFieldScope.custom)
  contractType?: string

  @entityField(NodeFieldScope.custom)
  contractEndDate?: string

  @entityRelation({
    backref: 'assets',
    getTargetNode: () => Customer,
    scope: NodeFieldScope.custom
  })
  customer?: RelationToMany<Customer>;

  @entityRelation({
    backref: 'assets',
    getTargetNode: () => Edge,
    scope: NodeFieldScope.common,
  })

  // @ts-ignore
  edges?: RelationToMany<Edge>;

  @entityRelation({
    backref: 'assets',
    getTargetNode: () => Location,
    scope: NodeFieldScope.custom
  })
  location?: RelationToMany<Location>;

  @entityField(NodeFieldScope.custom)
  isMigrated?: boolean;
  
  @entityField(NodeFieldScope.custom)
  ctrlType?: string;

  @entityField(NodeFieldScope.custom)
  address?: string;

  @entityField(NodeFieldScope.custom)
  deviceId?: string;

  @entityField(NodeFieldScope.custom)
  cjcserialNumber?: string;

  @entityField(NodeFieldScope.custom)
  systemOfMeasurement?: string;
}
