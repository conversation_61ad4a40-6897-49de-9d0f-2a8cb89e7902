import { BaseService, SelectParams } from "@carrier-io/backend-lib-node-sdk";
import { Edge } from "../entities/Edge";
import { logger } from '../utils/logger';

interface UpsertResponse {
  errorMessage?: string;
  isSuccess: boolean;
  node: Edge;
}

export class GatewayInfoHandlingService {
  private edgeService: BaseService<Edge>;

  constructor() {
    this.edgeService = new BaseService<any>(Edge);
  }
  
  /**
   * Checks if gateway details exist for the given gateway ID and serial number
   * @param gwid Gateway ID to check
   * @param serialNumber Serial number to check
   * @returns Promise resolving to edge details or null if not found
   */
  public async checkGatewayExists(
    gwid: string,
    serialNumber: string
  ): Promise<Edge[] | null> {
    try {
      const params: SelectParams<Edge> = {
        filters: {
          edgeId: { equalTo: gwid },
          serialNumber: { equalTo: serialNumber }
        },
      };
      const edgeDetails = await this.edgeService.select(params);
      logger.info(`Gateway details for ${gwid}:`, JSON.stringify(edgeDetails))
      return edgeDetails?.length ? edgeDetails : null;
    } catch (err) {
      logger.error(`Error checking gateway details for ${gwid}:`, err);
      throw err;
    }
  }

  /**
   * Checks if a gateway exists
   * @param gwid Gateway ID to check
   * @param serialNumber Gateway serial number to check
   * @returns Boolean indicating if the gateway exists
   */
  public async gatewayExists(
    gwid: string,
    serialNumber: string
  ): Promise<any> {
    const details = await this.checkGatewayExists(gwid, serialNumber);
    return details;
  }

  /**
   * Gets and updates old gateway details with new gateway information
   * @param oldGwid Old gateway ID
   * @param newGwid New gateway ID
   * @param oldGwSerialNumber Old gateway serial number
   * @param newGwSerialNumber New gateway serial number
   * @returns Number of successful updates or null if gateway not found
   */
  public async getAndUpdateOldGatewayDetails(
    oldGwid: string,
    newGwid: string,
    oldGwSerialNumber: string,
    newGwSerialNumber: string,
    newGatewayNodeId: string
  ): Promise<number | null> {
    try {
      // For update operation, we need both gateways
      const oldEdgeDetails = await this.checkGatewayExists(oldGwid, oldGwSerialNumber);
      
      // Validate edge details
      if (!oldEdgeDetails || !oldEdgeDetails.length) {
        console.debug(`Old Gateway Information is missing cannot proceed with update: ${oldGwid}`);
        return null;
      }

      // Update edge details
      const updatedEdgeViews = oldEdgeDetails.map(({ createdBy, updatedBy, createdAt, updatedAt, isDeleted, hasPoint, assets, ...cleanedEdge }) => ({
        ...cleanedEdge,
        isEdgeReplaced: true,
        replacedEdgeSN: newGwSerialNumber,
        replacedEdgeId: newGatewayNodeId
      }));

      if (!updatedEdgeViews.length) return 0;
      
      logger.info(`Updating ${updatedEdgeViews.length} gateway records`);  
      // Batch upsert for larger datasets
      const batchSize = 20;
      let successCount = 0;
      let totalProcessed = 0;
      
      for (let i = 0; i < updatedEdgeViews.length; i += batchSize) {
        const batch = updatedEdgeViews.slice(i, i + batchSize);
        logger.info(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(updatedEdgeViews.length / batchSize)}`);
        const res = await this.edgeService.upsert(batch) as UpsertResponse[];
        logger.info("Old gateway details updated ", JSON.stringify(res));
        const batchSuccessCount = res.filter(item => item.isSuccess).length;
        successCount += batchSuccessCount;
        totalProcessed += res.length;
        logger.debug(`Batch success: ${batchSuccessCount}/${res.length}`);
      }

      logger.info(`Total success: ${successCount}/${totalProcessed}`);
      return successCount;
    } catch (err) {
      logger.error("Error updating gateway details:", err);
      throw err;
    }
  }
}