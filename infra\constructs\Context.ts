import { Construct } from "constructs";
const STANDARD_STAGES = new Set([
  "dev",
  "qa",
  "preprod",
  "prod"
]);

export interface ContextProps {
  stage: string;
  partition: string;
}

export class Context extends Construct {
  private stage: string;

  public nodeApiUrl: string;

  public platformToken: string;

  public carrierAccountId: string;

  public isPersonalEnvironment: boolean;

  public roleARN: string;

  public partition: string;

  public carrierIoRoleArn: string;

  public eventDbURL: string;

  public alarmDbURL : string;

  public secretManagerARN: string;

  public lookupApiUrl: string;

  constructor(scope: Construct, id: string, props: ContextProps) {
    super(scope, id);
    this.stage = props.stage;
    this.partition = props.partition;
    this.isPersonalEnvironment = !STANDARD_STAGES.has(props.stage);
    this.setNodeApiUrl();
    this.setPlatformToken();
    this.setCarrierIoAccount();
    this.setRoleARN();
    this.docDBURL();
    this.setSecretManagerARN();
    this.setLookupApiUrl();
  }

  private setLookupApiUrl() {
    const environmentLookup: any = {
      dev: {
        DEVICE_LOOKUP_API_URL: 'https://devicelookup.api.dev.carrier.io/graphql'
      },
      qa: {
        DEVICE_LOOKUP_API_URL: 'https://devicelookup.api.qa.carrier.io/graphql',
      },
      preprod: {
        DEVICE_LOOKUP_API_URL: 'https://devicelookup.api.preprod.carrier.io/graphql',
      },
      prod: {
        DEVICE_LOOKUP_API_URL: 'https://devicelookup.api.carrier.io/graphql',
      },
    };
    this.lookupApiUrl = environmentLookup[this.stage]?.DEVICE_LOOKUP_API_URL || environmentLookup.dev.DEVICE_LOOKUP_API_URL;
  }

  private setSecretManagerARN() {
    const environmentLookup: any = {
      'dev': 'arn:aws:secretsmanager:us-east-1:************:secret:ahp-alarm-documentdb-TuheV9',
      'qa': 'arn:aws:secretsmanager:us-east-1:************:secret:ahp-alarm-documentdb-qI3fPM',
      'preprod': 'arn:aws:secretsmanager:us-east-1:219096342814:secret:ahp-alarm-documentdb-Ds7pGt',
      'prod': 'arn:aws:secretsmanager:us-east-1:038434134771:secret:ahp-alarm-documentdb-MHUFB0',
      'dev-cn': 'arn:aws-cn:secretsmanager:cn-northwest-1:113658156983:secret:ahp-alarm-documentdb-isLMvC',
      'qa-cn': 'arn:aws-cn:secretsmanager:cn-northwest-1:124057467907:secret:ahp-alarm-documentdb-PydoqP',
      'preprod-cn': 'arn:aws-cn:secretsmanager:cn-northwest-1:125752601772:secret:ahp-alarm-documentdb-PThQdA',
      'prod-cn': 'arn:aws-cn:secretsmanager:cn-northwest-1:125779191723:secret:ahp-alarm-documentdb-SEJx72'
    };
    this.secretManagerARN = environmentLookup[this.stage] || environmentLookup.dev;
  }

  private docDBURL() {
    let eventDBURLs = {
      'dev': 'ahp-discovery-events.cluster-ceqyrfmgciyu.us-east-1.docdb.amazonaws.com',
      'qa': 'ahp-discovery-events.cluster-cszkw6tbydzm.us-east-1.docdb.amazonaws.com',
      'preprod': 'ahp-discovery-events.cluster-c5mlkixh0osx.us-east-1.docdb.amazonaws.com',
      'prod': 'ahp-discovery-events.cluster-c5atjqeg7vlg.us-east-1.docdb.amazonaws.com',
      "dev-cn": 'ahp-discovery-events.cluster-clo0cwoccjdj.docdb.cn-northwest-1.amazonaws.com.cn',
      'qa-cn': 'ahp-discovery-events.cluster-cl48222ymntv.docdb.cn-northwest-1.amazonaws.com.cn',
      'preprod-cn': 'ahp-discovery-events.cluster-cbocuoea82w1.docdb.cn-northwest-1.amazonaws.com.cn',
      'prod-cn': 'ahp-discovery-events.cluster-c92o8wguwntz.docdb.cn-northwest-1.amazonaws.com.cn',
    };
    let alarmDBURLs = {
      'dev': 'ahp-alarm-cluster.cluster-ceqyrfmgciyu.us-east-1.docdb.amazonaws.com',
      'qa': 'ahp-alarm-cluster.cluster-cszkw6tbydzm.us-east-1.docdb.amazonaws.com',
      'preprod': 'ahp-alarm-cluster.cluster-c5mlkixh0osx.us-east-1.docdb.amazonaws.com',
      'prod': 'ahp-alarm-cluster.cluster-c5atjqeg7vlg.us-east-1.docdb.amazonaws.com',
      "dev-cn": 'ahp-alarm-cluster.cluster-clo0cwoccjdj.docdb.cn-northwest-1.amazonaws.com.cn',
      'qa-cn': 'ahp-alarm-cluster.cluster-cl48222ymntv.docdb.cn-northwest-1.amazonaws.com.cn',
      'preprod-cn': 'ahp-alarm-cluster.cluster-cbocuoea82w1.docdb.cn-northwest-1.amazonaws.com.cn',
      'prod-cn': 'ahp-alarm-cluster.cluster-c92o8wguwntz.docdb.cn-northwest-1.amazonaws.com.cn',
    };
    this.eventDbURL = eventDBURLs[this.stage] || eventDBURLs.dev
    this.alarmDbURL = alarmDBURLs[this.stage] || alarmDBURLs.dev
  }

  private setNodeApiUrl() {
    const nodeApis: { [index: string]: string } = {
      dev: "jmuuec4wwie3m5r2isaydadily0uxjcm",
      qa: "djfpwkz3xp65woi2gyolt6rv3m0tdzgh",
      preprod: "np4c4x7dgner77khkvw7ymgmna0xgxov",
      prod: "l3mqtqvgockus2off27kq4kzaa0wgblc",
    };
    let baseUrl = "";
    if (this.isPersonalEnvironment) {
      baseUrl = `https://jmuuec4wwie3m5r2isaydadily0uxjcm.lambda-url.us-east-1.on.aws`;
    } else if(this.stage === "dev") {
       baseUrl = 'https://node.api.dev.carrier.io'
    }else {
      baseUrl = `https://${nodeApis[this.stage]}.lambda-url.us-east-1.on.aws`;
    }
    this.nodeApiUrl = `${baseUrl}/nodes/`;
  }

  private setCarrierIoAccount() {
    const devAccountId = "************";
    const accountIds = new Map<string, string>([
      ["dev", devAccountId],
      ["qa", "************"],
      ["preprod", "************"],
      ["prod", "************"],
    ]);

    this.carrierAccountId = accountIds.get(this.stage) ?? devAccountId;
  }

  private setPlatformToken() {
    const environmentLookup: any = {
      dev: {
        PLATFORM_TOKEN:
          "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************.YgiAYjb8GlkrJFVptu3VvNTCEdtf0kniD7YMFZujMYs",
      },
      qa: {
        PLATFORM_TOKEN:
          "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************.NqzezDFgdX0j6UpsQiLIiEl0a0GWOdSvka8hIBdT5To",
      },
      preprod: {
        PLATFORM_TOKEN:
          "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************.d5k8OO13TAx3SVHGJQRg2irH5a3HGwRjbAoz1gDTFoU",
      },
      prod: {
        PLATFORM_TOKEN:
          "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************.Id5_eiSe8FcVuw1agsAowqUXRSWwyw82TQ82x5WpUf8",
      },
    };
    this.platformToken =
      environmentLookup[this.stage]?.PLATFORM_TOKEN ||
      environmentLookup.dev.PLATFORM_TOKEN;
  }

  private setRoleARN() {
    this.carrierIoRoleArn = `arn:${this.partition}:iam::${this.carrierAccountId}:role/ZephyrPlatformRole-${this.stage}`;
  }
}
