import { NodeFieldScope, entityField, entityName } from '@carrier-io/backend-lib-node-sdk';
import { EdgeEntityClass } from '@carrier-io/io-lib-node-sdk-models';

@entityName('edge')
export class Edge extends EdgeEntityClass {
  @entityField(NodeFieldScope.custom)
  edgeId?: string;

  @entityField(NodeFieldScope.custom)
  mac?: string;

  @entityField(NodeFieldScope.custom)
  serialNumber?: string;

  @entityField(NodeFieldScope.custom)
  sourceCSSevenEnv?: string;

  @entityField(NodeFieldScope.custom)
  workOrderId?: string;
  
  @entityField(NodeFieldScope.custom)
  isMigrated?: boolean;

  @entityField(NodeFieldScope.custom)
  isOnline?: boolean;

  @entityField(NodeFieldScope.custom)
  CSSInstance?: string;

  @entityField(NodeFieldScope.custom)
  imei?: string;

  @entityField(NodeFieldScope.custom)
  iccId?: string;

  @entityField(NodeFieldScope.custom)
  isEdgeReplaced?: boolean;

  @entityField(NodeFieldScope.custom)
  replacedEdgeSN?: string;

  @entityField(NodeFieldScope.custom)
  replacedEdgeId?: string;

  @entityField(NodeFieldScope.custom)
  model?: string;

}
