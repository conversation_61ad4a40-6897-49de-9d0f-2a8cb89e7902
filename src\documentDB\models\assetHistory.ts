import { Document, Types } from 'mongoose';

export interface Properties {
  key: string;
  value: string;
}
export interface Actor {
  type: string;
  cid: string;
}
export interface TargetEntity {
  type: string;
  chillerId?: string | undefined;
  cid: string;
}
export interface AssetHistory {
  createdAt: number;
  updatedAt: number;
  startTimestamp: number;
  actor: Actor;
  targetEntity: TargetEntity;
  reason: string | undefined;
  action: string;
  properties: Properties[];
}
export interface AssetHistoryInDb extends Document<Types.ObjectId>, AssetHistory {}
