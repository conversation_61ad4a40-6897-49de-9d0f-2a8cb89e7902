import { STSClient, AssumeRoleCommand } from "@aws-sdk/client-sts";
import { Credentials } from "../../src/models/Credentials";
import { logger } from "../../src/utils/logger";
let assumeCredentials: Credentials;
 
export class STSConfig {
  public async createSTSCredentials(): Promise<Credentials> {
    logger.info("in crreateSTS: ")
    const roleArn: string =
      process.env.CARRIERIO_ROLE_ARN ??
      "arn:aws:iam::550762557643:role/ZephyrPlatformRole-dev";
    const region: string = process.env.REGION ?? "us-east-1";
    const sts = new STSClient({ region });
 
    const assumeRoleCommand = new AssumeRoleCommand({
      RoleArn: roleArn,
      RoleSessionName: "ZephyrPlatformRole-dev",
      DurationSeconds: 3600,
    });
 
    try {
      const data = await sts.send(assumeRoleCommand);
      if (data.Credentials) {
        assumeCredentials = {
          accessKeyId: data.Credentials.AccessKeyId!,
          secretAccessKey: data.Credentials.SecretAccessKey!,
          sessionToken: data.Credentials.SessionToken!,
          region: region,
          expiration: new Date(data.Credentials.Expiration!),
        };
        return assumeCredentials;
      }
    } catch (error) {
      logger.error(`Error assuming role:: ${error}`);
    }
 
    return Promise.reject("Failed to assume role");
  }
 
  public async getSTSCredentials() {
    if(!assumeCredentials?.expiration) {
      logger.info("feching new cred");
      return await this.createSTSCredentials();
    }else if (
      assumeCredentials?.expiration &&
      assumeCredentials?.expiration.getTime() - Date.now() < 300000
    ) {
      logger.warn("Credentials are expired. Fetch new credentials.");
      return await this.createSTSCredentials();
    } 
    else {
      return assumeCredentials;
    }
  }
}