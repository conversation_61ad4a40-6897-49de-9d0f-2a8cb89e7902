import { BaseService, SelectParams } from "@carrier-io/backend-lib-node-sdk";
import { DeviceView } from "../entities/DeviceView";
import { v5 as uuidv5 } from 'uuid';
import { logger } from '../utils/logger';

export class DeviceViewInfoHandlingService {
  private deviceViewService: BaseService<DeviceView>;
  private readonly uuidNameSpace = "944640ed-f732-49c0-84e8-a02d4e527b89";
  private readonly batchSize = 25;
  
  /**
   * Generates a UUID for a device view
   * @param gwid The gateway ID
   * @param assetSerialNumber The asset serial number
   * @returns Generated UUID
   */
  private generateDeviceViewId(gwid: string, assetSerialNumber: string): string {
    const combinedString = [gwid, assetSerialNumber].join('-');
    logger.info(`Generating UUID for: ${combinedString}`);
    const generatedId = uuidv5(combinedString, this.uuidNameSpace);
    logger.info(`Generated UUID: ${generatedId}`);
    return generatedId;
  }

  constructor() {
    this.deviceViewService = new BaseService<DeviceView>(DeviceView);
  }

  /**
   * Gets device views based on edge node ID and serial number
   * @param edgeNodeId The edge node ID
   * @param edgeSerialNumber The edge serial number
   * @returns Array of device views
   */
  public async getDeviceViews(edgeNodeId: string, edgeSerialNumber: string): Promise<DeviceView[]> {
    try {
      const params: SelectParams<DeviceView> = {
        filters: {
          edgeNodeId: { equalTo: edgeNodeId },
          edgeSerialNumber: { equalTo: edgeSerialNumber },
          isDeleted: { equalTo: false }
        },
      };

      const count = await this.deviceViewService.count(params);
      if (count === 0) return [];
      
      logger.info("device view count:", count);
      
      
      const fetchLimit = count > 50 ? 50 : count;
      let devices: DeviceView[] = [];
      
      // Use sequential fetching for all datasets to avoid type issues
      for (let offset = 0; offset < count; offset += fetchLimit) {
        params.slicing = { limit: fetchLimit, offset };
        const batch = await this.deviceViewService.select(params);
        devices.push(...batch);
      }
      
      logger.info("devices fetched:", devices.length);
      return devices;
    } catch (err) {
      logger.error("Error getting device views:", err);
      return [];
    }
  }

  /**
   * Deletes device views by their IDs
   * @param deviceViewIds Array of device view IDs to delete
   * @returns Promise<void>
   */
  public async deleteDeviceViews(deviceViewIds: string[]): Promise<void> {
    try {
      if (deviceViewIds.length) {
        await this.deviceViewService.delete(deviceViewIds);
      }
    } catch (err) {
      logger.error("Error deleting device views:", JSON.stringify(err));
    }
  }

  /**
   * Updates device views with new edge information
   * @param deviceViews Array of device views to update
   * @param newEdgeNodeId The new edge node ID
   * @param newEdgeSerialNumber The new edge serial number
   * @param newGwid The new gateway ID
   * @returns Number of successfully updated records
   */
  public async updateDeviceViews(
    deviceViews: DeviceView[], 
    newEdgeNodeId: string, 
    newEdgeSerialNumber: string,
    newGwid: string
  ): Promise<number> {
    if (!deviceViews.length) return 0;
    
    try {
      // Filter out records with missing assetSerialNumber and prepare the rest for update
      const updatedDeviceViews = deviceViews
        .filter(device => {
          if (!device.assetSerialNumber) {
            logger.warn(`WARNING: Skipping device with missing assetSerialNumber, ID: ${device.id}`);
            return false;
          }
          return true;
        })
        .map(({ createdBy, updatedBy, createdAt, updatedAt, isDeleted, assetSerialNumber, ...rest }) => ({
          ...rest,
          assetSerialNumber,
          edgeNodeId: newEdgeNodeId,
          edgeSerialNumber: newEdgeSerialNumber,
          id: this.generateDeviceViewId(newGwid, assetSerialNumber!)
        }));

      logger.info(`Updating ${updatedDeviceViews.length} device views (skipped ${deviceViews.length - updatedDeviceViews.length} with missing assetSerialNumber)`);
      
      // Use sequential processing to avoid type issues
      let totalUpdated = 0;
      for (let i = 0; i < updatedDeviceViews.length; i += this.batchSize) {
        const batch = updatedDeviceViews.slice(i, i + this.batchSize);
        logger.info(`Processing batch ${Math.floor(i / this.batchSize) + 1}/${Math.ceil(updatedDeviceViews.length / this.batchSize)}`);
        const res = await this.deviceViewService.upsert(batch);
        totalUpdated += res.length;
      }
      
      return totalUpdated;
    } catch (err) {
      logger.warn("Error updating device views:", err);
      return 0;
    }
  }
}