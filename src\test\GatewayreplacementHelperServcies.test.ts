import 'reflect-metadata';

// Mock all external dependencies before any imports
const mockSelect = jest.fn();
const mockCount = jest.fn();
const mockUpsert = jest.fn();
const mockDelete = jest.fn();
const mockInsertGatewayHistory = jest.fn();
const mockInsertAssetHistory = jest.fn();
const mockGetSTSCredentials = jest.fn();

jest.mock('@carrier-io/backend-lib-node-sdk', () => ({
  BaseService: jest.fn().mockImplementation(() => ({
    select: mockSelect,
    count: mockCount,
    upsert: mockUpsert,
    delete: mockDelete
  }))
}));

jest.mock('../documentDB/repositories/event.repository.js', () => ({
  EventRepository: jest.fn().mockImplementation(() => ({
    insertGatewayHistory: mockInsertGatewayHistory,
    insertAssetHistory: mockInsertAssetHistory
  }))
}));

jest.mock('../../infra/config/sts-config.js', () => ({
  STSConfig: jest.fn().mockImplementation(() => ({
    getSTSCredentials: mockGetSTSCredentials
  }))
}));

jest.mock('uuid', () => ({
  v5: jest.fn(() => 'test-uuid-123')
}));

global.fetch = jest.fn();

describe('Individual Service Coverage Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('SecretManagerService Individual Coverage', () => {
    it('should test getSecretValue method directly', async () => {
      const mockSecretsManagerClient = {
        send: jest.fn()
      };
      
      jest.doMock('@aws-sdk/client-secrets-manager', () => ({
        SecretsManagerClient: jest.fn(() => mockSecretsManagerClient),
        GetSecretValueCommand: jest.fn()
      }));
      
      const { SecretManager } = await import('../services/secret-manager.service.js');
      const service = new SecretManager();
      
      const mockResponse = { SecretString: 'test-secret' };
      mockSecretsManagerClient.send.mockResolvedValueOnce(mockResponse);
      
      const result = await service.getSecretValue('test-secret-id');
      expect(result).toEqual(mockResponse);
    });
  });

  // Skip entity-dependent services due to decorator issues
  describe.skip('GatewayInfoHandlingService Individual Coverage', () => {
    it('should test checkGatewayExists method directly', async () => {
      const { GatewayInfoHandlingService } = await import('../services/GatewayInfoHandlingService.js');
      const service = new GatewayInfoHandlingService();

      // Test found case
      mockSelect.mockResolvedValueOnce([{ id: 'gw1', edgeId: 'edge1' }]);
      let result = await service.checkGatewayExists('edge1', 'sn1');
      expect(result).toEqual([{ id: 'gw1', edgeId: 'edge1' }]);

      // Test not found case
      mockSelect.mockResolvedValueOnce([]);
      result = await service.checkGatewayExists('edge1', 'sn1');
      expect(result).toBeNull();

      // Test error case
      mockSelect.mockRejectedValueOnce(new Error('DB error'));
      await expect(service.checkGatewayExists('edge1', 'sn1')).rejects.toThrow('DB error');
    });

    it('should test gatewayExists method directly', async () => {
      const { GatewayInfoHandlingService } = await import('../services/GatewayInfoHandlingService.js');
      const service = new GatewayInfoHandlingService();

      mockSelect.mockResolvedValueOnce([{ id: 'gw1' }]);
      const result = await service.gatewayExists('edge1', 'sn1');
      expect(result).toEqual([{ id: 'gw1' }]);
    });

    it('should test getAndUpdateOldGatewayDetails method directly', async () => {
      const { GatewayInfoHandlingService } = await import('../services/GatewayInfoHandlingService.js');
      const service = new GatewayInfoHandlingService();

      // Test success case
      const mockGateway = {
        id: 'gw1',
        model: 'test',
        createdBy: 'user',
        updatedBy: 'user',
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false,
        hasPoint: true,
        assets: []
      };
      mockSelect.mockResolvedValueOnce([mockGateway]);
      mockUpsert.mockResolvedValueOnce([{ isSuccess: true, node: {} }]);

      let result = await service.getAndUpdateOldGatewayDetails('old', 'new', 'oldSN', 'newSN', 'newId');
      expect(result).toBe(1);

      // Test not found case
      mockSelect.mockResolvedValueOnce([]);
      result = await service.getAndUpdateOldGatewayDetails('old', 'new', 'oldSN', 'newSN', 'newId');
      expect(result).toBeNull();

      // Test empty update case
      mockSelect.mockResolvedValueOnce([{}]);
      result = await service.getAndUpdateOldGatewayDetails('old', 'new', 'oldSN', 'newSN', 'newId');
      expect(result).toBe(0);

      // Test error case
      mockSelect.mockResolvedValueOnce([{ id: 'gw1' }]);
      mockUpsert.mockRejectedValueOnce(new Error('Update failed'));
      await expect(service.getAndUpdateOldGatewayDetails('old', 'new', 'oldSN', 'newSN', 'newId')).rejects.toThrow();
    });
  });

  describe.skip('DeviceViewInfoHandlingService Individual Coverage', () => {
    it('should test getDeviceViews method directly', async () => {
      const { DeviceViewInfoHandlingService } = await import('../services/DeviceViewInfoHandlingService.js');
      const service = new DeviceViewInfoHandlingService();

      // Test success case
      mockCount.mockResolvedValueOnce(2);
      mockSelect.mockResolvedValueOnce([{ id: 'dv1' }, { id: 'dv2' }]);
      let result = await service.getDeviceViews('edge1', 'sn1');
      expect(result).toHaveLength(2);

      // Test count 0 case
      mockCount.mockResolvedValueOnce(0);
      result = await service.getDeviceViews('edge1', 'sn1');
      expect(result).toEqual([]);

      // Test pagination case
      mockCount.mockResolvedValueOnce(75);
      mockSelect
        .mockResolvedValueOnce(Array(50).fill({ id: 'dv1' }))
        .mockResolvedValueOnce(Array(25).fill({ id: 'dv2' }));
      result = await service.getDeviceViews('edge1', 'sn1');
      expect(result).toHaveLength(75);

      // Test error case
      mockCount.mockRejectedValueOnce(new Error('Count failed'));
      result = await service.getDeviceViews('edge1', 'sn1');
      expect(result).toEqual([]);
    });

    it('should test deleteDeviceViews method directly', async () => {
      const { DeviceViewInfoHandlingService } = await import('../services/DeviceViewInfoHandlingService.js');
      const service = new DeviceViewInfoHandlingService();

      // Test success case
      mockDelete.mockResolvedValueOnce(undefined);
      await service.deleteDeviceViews(['dv1', 'dv2']);
      expect(mockDelete).toHaveBeenCalledWith(['dv1', 'dv2']);

      // Test empty array case
      await service.deleteDeviceViews([]);
      expect(mockDelete).toHaveBeenCalledTimes(1); // Only called once above

      // Test error case (should not throw)
      mockDelete.mockRejectedValueOnce(new Error('Delete failed'));
      await expect(service.deleteDeviceViews(['dv1'])).resolves.not.toThrow();
    });

    it('should test updateDeviceViews method directly', async () => {
      const { DeviceViewInfoHandlingService } = await import('../services/DeviceViewInfoHandlingService.js');
      const service = new DeviceViewInfoHandlingService();

      // Test empty case
      let result = await service.updateDeviceViews([], 'newEdge', 'newSN', 'newGwid');
      expect(result).toBe(0);

      // Test success case
      mockUpsert.mockResolvedValueOnce([{ isSuccess: true, node: {} }]);
      result = await service.updateDeviceViews([{ assetSerialNumber: 'asset1', id: 'dv1' }] as any, 'newEdge', 'newSN', 'newGwid');
      expect(result).toBe(1);

      // Test filter null case
      mockUpsert.mockResolvedValueOnce([{ isSuccess: true, node: {} }]);
      result = await service.updateDeviceViews([
        { assetSerialNumber: 'asset1', id: 'dv1' },
        { assetSerialNumber: null, id: 'dv2' },
        { assetSerialNumber: undefined, id: 'dv3' }
      ] as any, 'newEdge', 'newSN', 'newGwid');
      expect(result).toBe(1);

      // Test batch processing
      const largeArray = Array(30).fill(0).map((_, i) => ({ assetSerialNumber: `asset${i}`, id: `dv${i}` }));
      mockUpsert
        .mockResolvedValueOnce(Array(25).fill({ isSuccess: true, node: {} }))
        .mockResolvedValueOnce(Array(5).fill({ isSuccess: true, node: {} }));
      result = await service.updateDeviceViews(largeArray as any, 'newEdge', 'newSN', 'newGwid');
      expect(result).toBe(30);

      // Test error case
      mockUpsert.mockRejectedValueOnce(new Error('Upsert failed'));
      result = await service.updateDeviceViews([{ assetSerialNumber: 'asset1', id: 'dv1' }] as any, 'newEdge', 'newSN', 'newGwid');
      expect(result).toBe(0);
    });
  });

  describe('EventHistoryManagementService Individual Coverage', () => {
    it('should test addGatewayReplacementHistory method directly', async () => {
      const { EventHistoryManagementService } = await import('../services/EventHistoryManagementService.js');
      const service = new EventHistoryManagementService();

      const params = {
        oldGwSerialNumber: 'oldSN',
        newGwSerialNumber: 'newSN',
        oldGatewayNodeId: 'oldNode',
        newGatewayNodeId: 'newNode',
        oldGatewayModel: 'oldModel',
        newGatewayModel: 'newModel',
        replacementType: 'full'
      };

      // Test success case
      mockInsertGatewayHistory.mockResolvedValueOnce([]);
      let result = await service.addGatewayReplacementHistory(params);
      expect(result).toBe(true);

      // Test partial replacement
      mockInsertGatewayHistory.mockResolvedValueOnce([]);
      result = await service.addGatewayReplacementHistory({ ...params, replacementType: 'partial' });
      expect(result).toBe(true);

      // Test error case
      mockInsertGatewayHistory.mockRejectedValueOnce(new Error('Insert failed'));
      await expect(service.addGatewayReplacementHistory(params)).rejects.toThrow('Failed to add gateway replacement history');
    });

    it('should test addAssetHistory method directly', async () => {
      const { EventHistoryManagementService } = await import('../services/EventHistoryManagementService.js');
      const service = new EventHistoryManagementService();

      // Test success case
      mockInsertAssetHistory.mockResolvedValueOnce([]);
      const assetInfo = [{
        assetId: 'asset1',
        newGatewaySN: 'newSN',
        newGatewayId: 'newId',
        newGatewayModel: 'newModel',
        oldGatewaySN: 'oldSN',
        oldGatewayId: 'oldId',
        oldGatewayModel: 'oldModel',
        assetType: 'sensor'
      }];
      await service.addAssetHistory(assetInfo);
      expect(mockInsertAssetHistory).toHaveBeenCalled();

      // Test empty case
      await service.addAssetHistory([]);
      expect(mockInsertAssetHistory).toHaveBeenCalledTimes(1); // Only called once above

      // Test error case
      mockInsertAssetHistory.mockRejectedValueOnce(new Error('Insert failed'));
      await expect(service.addAssetHistory([{ assetId: 'asset1', assetType: 'sensor' }])).rejects.toThrow('Insert failed');
    });
  });

  describe.skip('LookupGqlService Individual Coverage', () => {
    it('should test replaceAndRetainEdgeNode method directly', async () => {
      process.env.DEVICE_LOOKUP_API_URL = 'https://test.api.com/graphql';
      process.env.DEVICE_LOOKUP_API_TOKEN = 'test-token';

      const { LookupGqlService } = await import('../services/lookup-gql-service.js');
      const service = new LookupGqlService();

      // Test STS error case
      mockGetSTSCredentials.mockRejectedValueOnce(new Error('STS failed'));
      await expect(service.replaceAndRetainEdgeNode({})).rejects.toBe('failed ReplaceAndRetainEdgeNode API');

      // Test success case
      mockGetSTSCredentials.mockResolvedValueOnce({
        accessKeyId: 'test',
        secretAccessKey: 'test',
        sessionToken: 'test',
        region: 'us-east-1'
      });
      
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        json: () => Promise.resolve({ data: { replaceAndRetainEdgeNode: { edgeId: 'test' } } })
      });

      const result = await service.replaceAndRetainEdgeNode({ test: 'data' });
      expect(result).toEqual({ edgeId: 'test' });

      // Test GraphQL error case
      mockGetSTSCredentials.mockResolvedValueOnce({
        accessKeyId: 'test',
        secretAccessKey: 'test',
        sessionToken: 'test',
        region: 'us-east-1'
      });
      
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        json: () => Promise.resolve({}) // No data field
      });

      await expect(service.replaceAndRetainEdgeNode({ test: 'data' })).rejects.toBe('failed ReplaceAndRetainEdgeNode API');

      // Test fetch error case
      mockGetSTSCredentials.mockResolvedValueOnce({
        accessKeyId: 'test',
        secretAccessKey: 'test',
        sessionToken: 'test',
        region: 'us-east-1'
      });
      
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Fetch failed'));
      await expect(service.replaceAndRetainEdgeNode({ test: 'data' })).rejects.toBe('failed ReplaceAndRetainEdgeNode API');
    });
  });
});