////////////////////////////
// IMPORTS
////////////////////////////
import { resolve } from "path";
import {
  Stack,
  StackProps,
  aws_iam as Iam,
  ArnFormat,
  Stage,
  Duration,
  aws_events as Events,
  aws_events_targets as Targets,
  aws_lambda as Lambda
} from "aws-cdk-lib";
import {
  BasicNodeJsFunction,
  ConfigManifest,
} from "@zephyr/backend-lib-infrastructure";
import { Context } from "../constructs/Context";
import { Config } from '../config/Config';
import { Runtime } from "aws-cdk-lib/aws-lambda";
import { IVpc } from 'aws-cdk-lib/aws-ec2';

////////////////////////////
// EXPORTS
////////////////////////////

interface LambdaStackProps extends StackProps {
	vpc: IVpc;
}
export class CjcCsSevenIntegrationStack extends Stack {
  private context: Context;
  private lambdaFunction: BasicNodeJsFunction;
  private stage: string;
  private roleARN: string;
  ////////////////////////////
  // CONSTRUCTOR
  ////////////////////////////
  constructor(private readonly appStage: Stage, private readonly config: ReturnType<typeof Config.from>, id: string, props: LambdaStackProps) {
    super(appStage, id, props);
    this.stage = appStage.stageName;

    this.config = config;
    this.context = new Context(this, "context", {
      stage: this.stage,
      partition: this.partition,
    });
    console.log(this.stage, "stage name");
    this.createLambdas(props);
    this.createEventRule();
  }

  ////////////////////////////
  // PRIVATE FUNCTIONS
  ////////////////////////////

  private createLambdas(props: LambdaStackProps) {
    this.lambdaFunction = new BasicNodeJsFunction(this, "ahp-CSSeven-sync", {
      entry: `${__dirname}/../../dist/src/lambda/CSSevenSyncLambda.js`,
      handler: "index.CSSevenSyncLambda.handler",
      runtime: Runtime.NODEJS_18_X,
      functionName: ConfigManifest.applyNameSpace(
        "CSSevenSyncLambda",
        this.stage
      ),
      timeout: Duration.minutes(3),
      memorySize: 1024,
      environment: {
        OTEL_SERVICE_NAME: `${this.config.otelServiceName}-${this.appStage.stageName}`,
        OTEL_LOG_LEVEL: JSON.stringify(this.config.otelLogLevel),
        AWS_LAMBDA_EXEC_WRAPPER: this.config.lambdaExecWrapper,
        OTEL_BACKEND_EXPORTERS: JSON.stringify(this.config.otelBackendExporters),
        OTEL_LOGS_SAMPLING_RATE: this.config.otelLogsSamplingRate,
        DISABLE_LOG_SAMPLING: this.config.disableLogSampling,
        ["ASSET_UPSERT_PRE_HOOK"]: ConfigManifest.applyNameSpace(
          "carrier-io-domain-assets-upsert-asset-hook",
          this.stage
        ),
        ["EDGE_UPSERT_PRE_HOOK"]: ConfigManifest.applyNameSpace(
          "carrier-io-domain-edges-upsert-edge-pre-hook",
          this.stage
        ),
        NODE_API_URL: this.context.nodeApiUrl,
        PLATFORM_TOKEN: this.context.platformToken,
        CARRIERIO_ROLE_ARN: this.context.carrierIoRoleArn,
        REGION: process.env.REGION ?? "us-east-1",
        NODE_API_TIMEOUT:"30000",
        ["ASSET_TOPIC"]: "spBv1.0/zephyr/DBIRTH",
        ["EDGE_TOPIC"]: "spBv1.0/zephyr/NBIRTH",
        ["ASSET_DESIGN_DATA_ZEPHYR_TABLE"]: `asset-design-data-${this.appStage.stageName}`,
        ["UUID_NAME_SPACE"]: "944640ed-f732-49c0-84e8-a02d4e527b89",
        ['EVENT_DB_URL']: `mongodb://<username>:<password>@${this.context.eventDbURL}:27017/ahp-discovery-events?ssl=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false`,
        ['ALARM_DB_URL']: `mongodb://<username>:<password>@${this.context.alarmDbURL}:27017/mute?ssl=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false`,
        ...this.createEnvironment()
      },
      vpc: props.vpc
     
    });

    this.configureOTELLogLibraryLambdaLayer(this.lambdaFunction);
    this.grantLambdaAccessToExecuteOtelLayer(this.lambdaFunction);

    const tableResource = this.formatArn({
      service: "dynamodb",
      arnFormat: ArnFormat.SLASH_RESOURCE_NAME,
      resource: "table",
      resourceName: `asset-design-data-${this.appStage.stageName}`,
    });
    const eventArn = this.formatArn({
      service: "events",
      arnFormat: ArnFormat.SLASH_RESOURCE_NAME,
      resource: "event-bus",
      resourceName: "default",
    });
    this.lambdaFunction.addToRolePolicy(
      new Iam.PolicyStatement({
        effect: Iam.Effect.ALLOW,
        actions: [
          "dynamodb:GetItem",
          "dynamodb:DescribeStream",
          "dynamodb:DescribeTable",
          "dynamodb:BatchGetItem",
          "dynamodb:CreateTable",
          "dynamodb:Delete*",
          "dynamodb:Update*",
          "dynamodb:PutItem",
          "dynamodb:Query",
          "dynamodb:Scan",
          "dynamodb:BatchWrite*",
        ],
        resources: [tableResource],
      })
    );
    this.lambdaFunction.addToRolePolicy(
      new Iam.PolicyStatement({
        effect: Iam.Effect.ALLOW,
        actions: ["sts:AssumeRole"],
        resources: [this.context.carrierIoRoleArn],
      })
    );
    this.lambdaFunction.addToRolePolicy(
      new Iam.PolicyStatement({
        effect: Iam.Effect.ALLOW,
        actions: ["events:PutEvents"],
        resources: [eventArn],
      })
    );
    this.lambdaFunction.addToRolePolicy(
			new Iam.PolicyStatement({
				actions: ['secretsmanager:GetSecretValue'],
				resources: [this.context.secretManagerARN],
			}) as any,
		);
  }

  private createEventRule() {
    // Create an EventBridge rule on the default event bus
    const rule = new Events.Rule(this, "EventRule", {
      ruleName: "CJC-CS7Integration-Adapter",
      eventBus: Events.EventBus.fromEventBusArn(
        this,
        "DefaultEventBus",
        `arn:aws:events:${this.region}:${this.account}:event-bus/default`
      ),
      eventPattern: {
        source: ["masterDataHandler.application"],
        detailType: ["cs7-data"],
      },
    });

    rule.addTarget(new Targets.LambdaFunction(this.lambdaFunction));
  }

  private createEnvironment() {
		// for NA ==> PROD Credentials will be shared with remaining environments
		const environmentLookup = {
			dev: {
				
				['DEVICE_LOOKUP_API_URL']: 'https://devicelookup.api.dev.carrier.io/graphql',
				['DEVICE_LOOKUP_API_TOKEN']: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************.YgiAYjb8GlkrJFVptu3VvNTCEdtf0kniD7YMFZujMYs',
				['SECRET_DB_KEY']: 'ahp-alarm-documentdb',
				['SECRET_DB_ARN']: 'arn:aws:secretsmanager:us-east-1:************:secret:ahp-alarm-documentdb-TuheV9'
			},
			qa: {
				['DEVICE_LOOKUP_API_URL']: 'https://devicelookup.api.qa.carrier.io/graphql',
				['DEVICE_LOOKUP_API_TOKEN']: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************.NqzezDFgdX0j6UpsQiLIiEl0a0GWOdSvka8hIBdT5To',
				['SECRET_DB_KEY']: 'ahp-alarm-documentdb',
				['SECRET_DB_ARN']: 'arn:aws:secretsmanager:us-east-1:279579021062:secret:ahp-alarm-documentdb-qI3fPM'
			},
			preprod: {
				['DEVICE_LOOKUP_API_URL']: 'https://devicelookup.api.preprod.carrier.io/graphql',
				['DEVICE_LOOKUP_API_TOKEN']: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwbGF0Zm9ybU5hbWUiOiJ6ZXBoeXIiLCJpYXQiOjE2NzM5NjIwMjV9.d5k8OO13TAx3SVHGJQRg2irH5a3HGwRjbAoz1gDTFoU',
				['SECRET_DB_KEY']: 'ahp-alarm-documentdb',
				['SECRET_DB_ARN']: 'arn:aws:secretsmanager:us-east-1:219096342814:secret:ahp-alarm-documentdb-Ds7pGt'
			},
			prod: {
				['DEVICE_LOOKUP_API_URL']: 'https://devicelookup.api.carrier.io/graphql',
				['DEVICE_LOOKUP_API_TOKEN']: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwbGF0Zm9ybU5hbWUiOiJ6ZXBoeXIiLCJpYXQiOjE2NzM5Njg0NDV9.Id5_eiSe8FcVuw1agsAowqUXRSWwyw82TQ82x5WpUf8',
				['SECRET_DB_KEY']: 'ahp-alarm-documentdb',
				['SECRET_DB_ARN']: 'arn:aws:secretsmanager:us-east-1:************:secret:ahp-alarm-documentdb-MHUFB0'
			},
		}
		return environmentLookup[this.stage] ?? environmentLookup["dev"]
	}

  private configureOTELLogLibraryLambdaLayer(lambdaInstance: BasicNodeJsFunction) {
    const otelLayer = Lambda.LayerVersion.fromLayerVersionArn(
      this,
      `otel-initializer-${lambdaInstance.functionName}`,
      `arn:${this.partition}:lambda:${this.config.otelLogServiceAccountRegion}:${this.config.otelLogServiceAccount}:layer:otel-initializer:${this.config.otelLogServiceAccountLambdaVersion}`,
    );
    lambdaInstance.addLayers(otelLayer);
  }

  private grantLambdaAccessToExecuteOtelLayer(lambdaInstance: BasicNodeJsFunction) {
    lambdaInstance.addToRolePolicy(
      new Iam.PolicyStatement(
        new Iam.PolicyStatement({
          effect: Iam.Effect.ALLOW,
          actions: ['ssm:GetParameter*'],
          resources: ['*'],
        }),
      ),
    );
  }
}
