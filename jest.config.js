const { compilerOptions } = require('./tsconfig.json');

module.exports = {
  preset: 'ts-jest',
  testMatch: ['**/+(*.)+(spec|test).+(ts|js)?(x)'],
  resolver: 'ts-jest-resolver',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  testEnvironment: 'node',
  moduleFileExtensions: ['ts', 'js'],
  modulePathIgnorePatterns: [
    '<rootDir>/dist/'
  ],
  transform: {
    '^.+\\.ts?$': 'ts-jest',
  },
  setupFiles: ['<rootDir>/src/test/config/jest.setupEnv.ts'],
  testResultsProcessor: 'jest-sonar-reporter',
  coverageDirectory: './coverage',
  collectCoverage: true,
  coverageReporters: ['lcov', 'text'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/test/**',
    '!src/**/*.test.ts',
    '!src/**/*.spec.ts',
    '!src/mocks/**',
    '!src/entities/**',
    '!src/adapters/**',
    '!src/documentDB/repositories/**',
    '!src/repositories/**',
    '!src/enums/**',
    '!src/gql/models/**',
    '!src/gql/query/**'
  ],
  verbose: true,
  testTimeout: 100000
};

// vim:expandtab:ft=javascript:sw=2:ts=2
