import 'reflect-metadata';

// Setup environment variables for testing
process.env.NODE_ENV = 'test';
process.env.DEVICE_LOOKUP_API_URL = 'https://test-api.example.com/graphql';
process.env.DEVICE_LOOKUP_API_TOKEN = 'test-token';

// Mock console methods to reduce noise in tests
console.log = jest.fn();
console.error = jest.fn();
console.warn = jest.fn();

// Mock logger from utils/logger to reduce noise in tests
jest.mock('../utils/logger', () => ({
	logger: {
		info: jest.fn(),
		error: jest.fn(),
		warn: jest.fn(),
		debug: jest.fn(),
	},
}));