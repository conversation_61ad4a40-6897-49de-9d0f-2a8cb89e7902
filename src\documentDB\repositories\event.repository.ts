import mongoose, { Mongoose, Model } from 'mongoose';
import { EventInDb, OobEvent, AssetHistory, AssetHistoryInDb } from '../models';
import { EventModel, AssetHistoryModel } from '../shemas';
import { MongoDBAdapter } from '../../adapters/mongodb';
import { logger } from '@carrier-io/pe-lib-otel-logs';

export class EventRepository {
  private readonly edgeModel: Model<EventInDb>
  private readonly assetModel: Model<AssetHistoryInDb>
  private muteInstance: Mongoose | undefined;
  private eventInstance: Mongoose | undefined;

  constructor() {
    this.edgeModel = EventModel;
    this.assetModel = AssetHistoryModel;
  }

  async insertGatewayHistory(eventsArr: OobEvent[]) {
    try{
        if(!this.eventInstance){
          if(this.muteInstance){
            await mongoose.disconnect();
          }
          this.eventInstance = await new MongoDBAdapter('event').connect();
        }
        return this.edgeModel.create(eventsArr, null);
    }catch(err){
        logger.error(`insertGatewayHistory`,err);
        throw err;
    }
  }

  async insertAssetHistory(eventsArr: AssetHistory[]) {
    try{
        if(!this.muteInstance){
          if(this.eventInstance){
            await mongoose.disconnect();
          }
          this.muteInstance = await new MongoDBAdapter('mute').connect();
        }
        return this.assetModel.create(eventsArr, null);
    }catch(err){
        logger.error(`insertAssetHistory`,err);
        throw err;
    }
  }
}
