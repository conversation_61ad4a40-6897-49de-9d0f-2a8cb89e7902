import { Document, Types } from 'mongoose';

export interface Properties {
  key: string;
  value: string;
}

export interface OobEvent {
  timestamp: number;
  type: string;
  edgeSN: string;
  edgeId: string | undefined;
  assetSN?: string;
  assetName?: string;
  assetId?: string;
  name: string | undefined;
  serialPort?: string;
  busAddress?: string;
  properties: Properties[] | undefined;
  originalDecodedMessage?: string;
}

export interface EventInDb extends Document<Types.ObjectId>, OobEvent {}
