import { LookupGqlService } from "./lookup-gql-service";
import { GatewayInfoHandlingService } from "./GatewayInfoHandlingService";
import { DeviceViewInfoHandlingService } from "./DeviceViewInfoHandlingService";
import { EventHistoryManagementService } from "./EventHistoryManagementService";
import { error } from "console";
import { logger } from '../utils/logger';

interface GatewayReplacementRequest {
  oldGwid: string,
  newGwid: string,
  fileName?: string,
  oldGwSerialNumber: string,
  newGwSerialNumber: string,
  newGatewayModel:string,
  replacementType:string
}

export class GatewayReplacementService {
  private readonly lookupService: LookupGqlService;
  private readonly gatewayInfoService: GatewayInfoHandlingService;
  private readonly deviceViewInfoService: DeviceViewInfoHandlingService;
  private readonly eventHistoryManagementService: EventHistoryManagementService;

  constructor() {
    this.lookupService = new LookupGqlService();
    this.gatewayInfoService = new GatewayInfoHandlingService();
    this.deviceViewInfoService = new DeviceViewInfoHandlingService();
    this.eventHistoryManagementService = new EventHistoryManagementService();
  }

  private validateRequest(payload: GatewayReplacementRequest): void {
    const required = ['oldGwid', 'newGwid', 'oldGwSerialNumber', 'newGwSerialNumber', 'newGatewayModel','replacementType'];
    const missing = required.filter(field => !payload[field as keyof GatewayReplacementRequest]);
    if (missing.length) {
      throw new Error(`Missing required fields: ${missing.join(', ')}`);
    }
  }

  /**
   * Handles gateway replacement requests
   * @param payload The gateway replacement request payload
   * @returns Response with success/failure status
   */
  public async handleGatewayReplacement(payload: GatewayReplacementRequest): Promise<any> {
    logger.info('Handling gateway replacement request:', payload);

    try {
      this.validateRequest(payload);
      const { oldGwid, newGwid, fileName, oldGwSerialNumber, newGwSerialNumber, newGatewayModel, replacementType = "full" } = payload;

      logger.debug(`Processing gateway replacement from ${oldGwid} to ${newGwid}`);
      
      // First check if old gateway details exist
      const oldGatewayDetails = await this.gatewayInfoService.gatewayExists(
        oldGwid, 
        oldGwSerialNumber
      );
      console.log("Old Gateway details", oldGatewayDetails);
      const oldGatewayNodeId : string = oldGatewayDetails?.[0]?.id ?? "";
      const oldGatewayModel: string = oldGatewayDetails?.[0]?.model ?? "";
      
      if (!oldGatewayDetails || !oldGatewayNodeId) {
        return {
          success: false,
          error: `Failed to process gateway replacement: Old gateway details not found for ${oldGwid}, ${oldGwSerialNumber}`,
          timestamp: new Date().toISOString()
        };
      }
      
      // Proceed with device lookup update
      const dlsResponse = await this.updateDeviceLookup(oldGwid, newGwid, []);
      if(!dlsResponse || !dlsResponse.edgeId){
        return {
          success: false,
          error: `Failed to process gateway replacement: ${error instanceof Error ? error.message : String(error)}`,
          timestamp: new Date().toISOString()
        }
      }
      const newGatewayNodeId: string = dlsResponse?.edgeId;
      // Now update the old gateway details
      const updateOldGateway = await this.gatewayInfoService.getAndUpdateOldGatewayDetails(
        oldGwid, 
        newGwid, 
        oldGwSerialNumber, 
        newGwSerialNumber,
        newGatewayNodeId
      );
      if(!updateOldGateway){
           return {
          success: false,
          error: `Failed to Update Old gateway: ${error instanceof Error ? error.message : String(error)}`,
          timestamp: new Date().toISOString()
        }
      }

      // Get and Update Device Views by constructing new node id (new gwid + assetSerialNumber)
      const existingDeviceViews = await this.deviceViewInfoService.getDeviceViews(
        oldGatewayNodeId,
        oldGwSerialNumber
      );
      if (existingDeviceViews.length > 0) {
        try {
           // Delete the Old Device  Views Before creating new ones
          await this.deviceViewInfoService.deleteDeviceViews(existingDeviceViews.map(view => view.id));
          console.log("Deleted device views of", oldGwid);
        } catch (err) {
          logger.error("Failed to delete old device views, continuing with update:", JSON.stringify(err));
        }

        // Update device views with new gateway information
        const updatedCount = await this.deviceViewInfoService.updateDeviceViews(
          existingDeviceViews,
          newGatewayNodeId,
          newGwSerialNumber,
          newGwid
        );
        logger.debug(`Updated ${updatedCount} device views with new gateway information`);
      } else {
        logger.warn("No device views found for the old gateway", oldGwid);
      }

      //Update  gateway History Events
      await this.eventHistoryManagementService.addGatewayReplacementHistory({
        oldGwSerialNumber,
        newGwSerialNumber,
        oldGatewayNodeId,
        newGatewayNodeId,
        oldGatewayModel,
        newGatewayModel,
        replacementType
      });

      //Update Asset History
      if (existingDeviceViews.length > 0) {
        const assetInfo = existingDeviceViews.map(asset => ({
          assetId: asset.id,
          oldEdgeId: oldGatewayNodeId,
          newEdgeId: newGatewayNodeId,
          oldEdgeModel: oldGatewayModel,
          newEdgeModel: newGatewayModel,
          oldGatewaySN: oldGwSerialNumber,
          newGatewaySN: newGwSerialNumber,
          assetType: asset.picControllerVersion
        }));
        console.log("updating asset History")
        await this.eventHistoryManagementService.addAssetHistory(assetInfo);
      }
   
      return {
        success: true,
        message: `Successfully processed gateway replacement from ${oldGwid} to ${newGwid}`,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error('Error processing gateway replacement:', error);
      return {
        success: false,
        error: `Failed to process gateway replacement: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: new Date().toISOString()
      };
    }
  }

  public async updateDeviceLookup(oldEdge : string, newEdge: string, assetSerialNumber: string[]) {

    const params = {
      groupName: 'ahp',
      oldEdgeNodeId: oldEdge,
      newEdgeNodeId: newEdge,
      assetSerialNumber
    }
    try {
      logger.debug("replaceAndRetainEdgeNode params", JSON.stringify(params));
      let result = await this.lookupService.replaceAndRetainEdgeNode(params);
      console.log("replaceAndRetainEdgeNode success", JSON.stringify(result));
      return result
    } catch (err) {
      logger.error("replaceAndRetainEdgeNode failed");
      throw new Error('DLS API update failed');
      
    }
  }

}