import { Stack as BaseStack, StackProps, Stage } from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';

interface VpcStackProps extends StackProps {
  stackId: string;
  environmentVariables: Record<string, any>;
}

export class VpcStack extends BaseStack {
  public vpc: ec2.IVpc;

  public constructor(
    private readonly stage: Stage,
    private readonly config,
    id: string,
    props?: VpcStackProps,
  ) {
    super(stage, id, props);

    this.vpc = ec2.Vpc.fromLookup(this, `${id}-vpc`, {
      vpcId: config.vpcId,
    });
  }
}
