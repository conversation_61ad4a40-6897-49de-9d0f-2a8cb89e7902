export const edgeUpsertMock = [
    {
        "isSuccess": true,
        "node": {
            "domain": "edge",
            "id": "80b6252c-fa2a-437c-9d1f-6af1f0a06b74",
            "createdAt": "2025-03-17T08:35:24.933Z",
            "createdBy": "carrier-io-pipeline-consumer-LifecycleConsumer-lambda-prod",
            "updatedAt": "2025-06-09T05:28:32.852Z",
            "updatedBy": "NodeOpenAPIHandler-prod-L4aYMjfPIAMEYHA=",
            "isDeleted": false,
            "birthNotification": 1743991736000,
            "deathNotification": 1743992308473,
            "name": "866226060854159",
            "edgeId": "866226060854159",
            "serialNumber": "KE1W000310",
            "model": "CJC_Eye",
            "isOnline": false,
            "imei": "866226060854159",
            "iccId": "8981100005707435625",
            "lastEventTimestamp": 1743992308473,
            "isEdgeReplaced": true,
            "replacedEdgeSN": "MPY25BJ5Z000102",
            "replacedEdgeId": "d6a6293b-918a-4711-93f8-3b30496fc3df"
        }
    }
]