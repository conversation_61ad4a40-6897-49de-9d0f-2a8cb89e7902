import {
  BaseNodeEntityClass,
  NodeFieldScope,
  RelationToMany,
  entityField,
  entityName,
  entityRelation,
} from '@carrier-io/backend-lib-node-sdk';

import { Asset } from './Asset';

@entityName('customer')
export class Customer extends BaseNodeEntityClass {
  @entityField(NodeFieldScope.custom)
  customerId: string;

  @entityField(NodeFieldScope.custom)
  name?: string;

  @entityField(NodeFieldScope.custom)
  contactName?: string;

  @entityField(NodeFieldScope.custom)
  contactEmail?: string;

  @entityRelation({
    getTargetNode: () => Asset,
    backref: 'customer',
    scope: NodeFieldScope.custom
  })
  assets?: RelationToMany<Asset>;
}
