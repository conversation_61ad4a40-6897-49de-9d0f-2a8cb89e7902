import "reflect-metadata";
import { BasicLambdaFunction } from "@carrier/backend-lib-core";
import { Context } from "aws-lambda";
import { CSSevenSyncService } from "../services/CSSevenSyncService";
import { GatewayReplacementService } from "../services/GatewayReplacementService";
import { logger } from '../utils/logger';
import { requestObj } from "../mocks/cssevenResponse";
// const { GATEWAY_REPLACEMENT } = require("../utils/constants");

export class CSSevenSyncLambda extends BasicLambdaFunction {
  static override async onRequest(event: any, context: Context): Promise<any> {
       
    // Check if this is a gateway replacement request
    if (event.action === "gatewayReplacement") {
      logger.info('Processing gateway replacement request');
      const gatewayService = new GatewayReplacementService();
      return gatewayService.handleGatewayReplacement(event.payload);
    }
    logger.info("ahp-csseven-sync-service started", event);
    // Regular CS7 data processing
    return CSSevenSyncService.processEvent(JSON.stringify(event), "dev");
  }
}